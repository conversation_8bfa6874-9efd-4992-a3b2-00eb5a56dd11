"""
Aura AI Companion - An empathetic AI assistant with deep emotional understanding.

This package implements a sophisticated AI companion that provides emotional support
through advanced emotion analysis, behavioral pattern recognition, and privacy-preserving
insights generation.

Key Features:
- Real-time emotion analysis and crisis detection
- Emotional Chain-of-Thought (ECoT) reasoning for empathetic responses
- Implicit Knowledge Graph for long-term memory and pattern recognition
- Privacy-preserving insights and therapist reports
- Multi-layered safety and crisis intervention systems

Main Components:
- ConversationService: Main orchestrator for conversation processing
- EmotionAnalysisService: Real-time emotional analysis
- InferenceEngine: Pattern recognition and insight generation
- KnowledgeGraphService: Long-term memory and context management
- ECoTService: Emotional reasoning and response generation
- CrisisService: Crisis detection and intervention
- ReportGenerator: Privacy-preserving insights and reports
"""

from .conversation.conversation_service import ConversationService
from .emotion.emotion_analysis_service import EmotionAnalysisService
from .inference.inference_engine import InferenceEngine
from .memory.knowledge_graph_service import KnowledgeGraphService
from .conversation.ecot_service import ECoTService
from .safety.crisis_service import CrisisService
from .reporting.report_generator import ReportGenerator
from .models.data_models import (
    EmotionProfile, ConversationMessage, InferredInsight, 
    IKGNode, IKGEdge, ECoTProcess, UserProfile
)
from .privacy import (
    apply_differential_privacy, anonymize_user_data, 
    privacy_audit_logger, consent_manager
)

__version__ = "1.0.0"
__author__ = "Aura AI Team"
__description__ = "An empathetic AI companion with deep emotional understanding"

# Main entry point for the Aura AI Companion
class AuraCompanion:
    """
    Main interface for the Aura AI Companion system.
    Provides a simplified API for integrating the companion into applications.
    """
    
    def __init__(self):
        """Initialize the Aura AI Companion with all core services."""
        self.conversation_service = ConversationService()
        print("Aura AI Companion initialized successfully.")
    
    async def chat(self, user_id: str, message: str) -> dict:
        """
        Process a user message and return a response.
        
        Args:
            user_id: Unique identifier for the user
            message: The user's message text
            
        Returns:
            Dictionary containing response and analysis metadata
        """
        return await self.conversation_service.process_user_message(user_id, message)
    
    async def get_user_insights(self, user_id: str) -> dict:
        """
        Get insights summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing user insights and patterns
        """
        return await self.conversation_service.get_user_insights_summary(user_id)
    
    async def generate_therapist_report(self, user_id: str, time_period_days: int = 30) -> dict:
        """
        Generate a privacy-preserving report for a therapist.
        
        Args:
            user_id: User identifier
            time_period_days: Number of days to include in analysis
            
        Returns:
            Dictionary containing the therapist report
        """
        report_generator = ReportGenerator()
        return await report_generator.generate_therapist_report(
            user_id, 
            self.conversation_service.knowledge_service, 
            time_period_days
        )
    
    async def create_user(self, user_id: str, preferences: dict = None, 
                         privacy_settings: dict = None) -> dict:
        """
        Create a new user profile.
        
        Args:
            user_id: Unique user identifier
            preferences: User preferences
            privacy_settings: Privacy settings
            
        Returns:
            Created user profile as dictionary
        """
        profile = await self.conversation_service.create_user_profile(
            user_id, preferences, privacy_settings
        )
        return profile.to_dict()
    
    async def get_conversation_history(self, user_id: str, limit: int = 50) -> list:
        """
        Get conversation history for a user.
        
        Args:
            user_id: User identifier
            limit: Maximum number of messages to return
            
        Returns:
            List of conversation messages
        """
        return await self.conversation_service.get_conversation_history(user_id, limit)
    
    async def clear_user_data(self, user_id: str) -> bool:
        """
        Clear all data for a user (for privacy compliance).
        
        Args:
            user_id: User identifier
            
        Returns:
            True if successful
        """
        return await self.conversation_service.clear_conversation_history(user_id)
    
    async def export_user_data(self, user_id: str) -> dict:
        """
        Export all user data for privacy compliance.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing all user data
        """
        return await self.conversation_service.export_user_data(user_id)

# Convenience function for quick setup
def create_aura_companion() -> AuraCompanion:
    """
    Create and return a new Aura AI Companion instance.
    
    Returns:
        Initialized AuraCompanion instance
    """
    return AuraCompanion()

__all__ = [
    'AuraCompanion',
    'create_aura_companion',
    'ConversationService',
    'EmotionAnalysisService', 
    'InferenceEngine',
    'KnowledgeGraphService',
    'ECoTService',
    'CrisisService',
    'ReportGenerator',
    'EmotionProfile',
    'ConversationMessage',
    'InferredInsight',
    'IKGNode',
    'IKGEdge',
    'ECoTProcess',
    'UserProfile',
    'apply_differential_privacy',
    'anonymize_user_data',
    'privacy_audit_logger',
    'consent_manager'
]
