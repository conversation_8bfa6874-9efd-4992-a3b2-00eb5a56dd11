"""
Inference Engine for the Aura AI Companion.
Analyzes conversation transcripts to extract implicit facts and patterns.
This is the heart of the "Observant Friend" feature.
"""
import re
import asyncio
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta
from collections import defaultdict
import uuid

from ..models.data_models import InferredInsight, ConversationMessage, EmotionProfile


class InferenceEngine:
    """
    Analyzes conversation transcripts to extract implicit facts and patterns.
    This is the heart of the "Observant Friend" feature.
    """
    
    def __init__(self):
        # Pre-compiled regex for efficient pattern matching, expanded for robustness
        self.sleep_patterns = re.compile(
            r'\b(good night|gonna sleep|sleepy|off to bed|gn|sleep well|going to sleep|time for bed|bedtime)\b', 
            re.IGNORECASE
        )
        self.wake_patterns = re.compile(
            r'\b(good morning|just woke up|morning everyone|gm|woke up|getting up|morning)\b', 
            re.IGNORECASE
        )
        self.exercise_keywords = {
            'run', 'running', 'gym', 'workout', 'exercise', 'walk', 'walking', 
            'yoga', 'hike', 'hiking', 'biked', 'biking', 'swim', 'swimming',
            'jog', 'jogging', 'fitness', 'training', 'pilates', 'dance', 'dancing'
        }
        self.stress_keywords = {
            'stress', 'stressed', 'anxious', 'anxiety', 'overwhelmed', 'burnt out', 
            'burnout', 'frustrated', 'frustration', 'pressure', 'worried', 'worry'
        }
        self.relationship_indicators = {
            'family': ['mom', 'dad', 'mother', 'father', 'sister', 'brother', 'parent', 'family'],
            'romantic': ['boyfriend', 'girlfriend', 'partner', 'husband', 'wife', 'dating'],
            'friends': ['friend', 'buddy', 'pal', 'bestie', 'bff'],
            'work': ['boss', 'colleague', 'coworker', 'manager', 'teammate']
        }
        print("InferenceEngine initialized.")

    async def infer_insights_from_transcript(self, transcript: List[ConversationMessage]) -> List[InferredInsight]:
        """
        Analyzes a full conversation transcript to generate a list of structured insights.
        
        Args:
            transcript: List of ConversationMessage objects
            
        Returns:
            List of InferredInsight objects containing discovered patterns
        """
        insights = []
        
        # Filter to user messages only for pattern analysis
        user_messages = [msg for msg in transcript if msg.sender == 'user']
        
        if not user_messages:
            return insights
        
        # Generate different types of insights
        sleep_insights = await self._analyze_sleep_patterns(user_messages)
        insights.extend(sleep_insights)
        
        coping_insights = await self._analyze_coping_mechanisms(user_messages)
        insights.extend(coping_insights)
        
        relationship_insights = await self._analyze_relationship_sentiment(user_messages)
        insights.extend(relationship_insights)
        
        routine_insights = await self._analyze_daily_routines(user_messages)
        insights.extend(routine_insights)
        
        mood_insights = await self._analyze_mood_patterns(user_messages)
        insights.extend(mood_insights)
        
        print(f"InferenceEngine: Analysis complete. Generated {len(insights)} insights.")
        return insights

    async def _analyze_sleep_patterns(self, messages: List[ConversationMessage]) -> List[InferredInsight]:
        """Analyze sleep and wake patterns from messages."""
        insights = []
        sleep_times = []
        wake_times = []
        
        for msg in messages:
            # Check for sleep patterns
            if self.sleep_patterns.search(msg.message):
                sleep_times.append({
                    'timestamp': msg.timestamp,
                    'message_id': msg.id,
                    'confidence': self._calculate_pattern_confidence(msg.message, self.sleep_patterns)
                })
            
            # Check for wake patterns
            if self.wake_patterns.search(msg.message):
                wake_times.append({
                    'timestamp': msg.timestamp,
                    'message_id': msg.id,
                    'confidence': self._calculate_pattern_confidence(msg.message, self.wake_patterns)
                })
        
        # Generate insights for sleep patterns
        if len(sleep_times) >= 3:  # Need multiple data points for pattern
            avg_sleep_hour = sum(t['timestamp'].hour for t in sleep_times) / len(sleep_times)
            confidence = sum(t['confidence'] for t in sleep_times) / len(sleep_times)
            
            insights.append(InferredInsight(
                insight_type='PotentialSleepTime',
                confidence_score=confidence,
                timestamp=datetime.now(),
                data={
                    'average_sleep_hour': avg_sleep_hour,
                    'sleep_time_count': len(sleep_times),
                    'pattern_consistency': self._calculate_time_consistency([t['timestamp'] for t in sleep_times])
                },
                source_message_ids=[t['message_id'] for t in sleep_times]
            ))
        
        # Generate insights for wake patterns
        if len(wake_times) >= 3:
            avg_wake_hour = sum(t['timestamp'].hour for t in wake_times) / len(wake_times)
            confidence = sum(t['confidence'] for t in wake_times) / len(wake_times)
            
            insights.append(InferredInsight(
                insight_type='PotentialWakeTime',
                confidence_score=confidence,
                timestamp=datetime.now(),
                data={
                    'average_wake_hour': avg_wake_hour,
                    'wake_time_count': len(wake_times),
                    'pattern_consistency': self._calculate_time_consistency([t['timestamp'] for t in wake_times])
                },
                source_message_ids=[t['message_id'] for t in wake_times]
            ))
        
        return insights

    async def _analyze_coping_mechanisms(self, messages: List[ConversationMessage]) -> List[InferredInsight]:
        """Analyze stress coping mechanisms and their effectiveness."""
        insights = []
        stress_events = []
        
        # Find stress events
        for msg in messages:
            if not msg.emotion_profile:
                continue
                
            # Check for stress indicators
            is_stressed = (
                any(keyword in msg.message.lower() for keyword in self.stress_keywords) or
                msg.emotion_profile.sentiment_polarity < -0.3 or
                msg.emotion_profile.arousal_level > 0.7
            )
            
            if is_stressed:
                stress_events.append({
                    'timestamp': msg.timestamp,
                    'message_id': msg.id,
                    'stress_level': abs(msg.emotion_profile.sentiment_polarity) + msg.emotion_profile.arousal_level
                })
        
        # Look for coping mechanisms after stress events
        for stress_event in stress_events:
            # Look for activities within 24 hours after stress
            window_start = stress_event['timestamp']
            window_end = window_start + timedelta(hours=24)
            
            subsequent_messages = [
                msg for msg in messages 
                if window_start < msg.timestamp <= window_end and msg.emotion_profile
            ]
            
            for msg in subsequent_messages:
                # Check for exercise keywords
                if any(keyword in msg.message.lower() for keyword in self.exercise_keywords):
                    # Check if mood improved
                    if msg.emotion_profile.sentiment_polarity > 0.3:
                        insights.append(InferredInsight(
                            insight_type='PositiveCopingMechanism',
                            confidence_score=0.8,
                            timestamp=datetime.now(),
                            data={
                                'trigger': 'stress',
                                'mechanism': 'exercise',
                                'outcome': 'positive',
                                'stress_level': stress_event['stress_level'],
                                'improvement_score': msg.emotion_profile.sentiment_polarity,
                                'time_to_coping': (msg.timestamp - stress_event['timestamp']).total_seconds() / 3600
                            },
                            source_message_ids=[stress_event['message_id'], msg.id]
                        ))
        
        return insights

    def _calculate_pattern_confidence(self, message: str, pattern: re.Pattern) -> float:
        """Calculate confidence score for pattern matches."""
        matches = pattern.findall(message.lower())
        if not matches:
            return 0.0
        
        # Higher confidence for more explicit statements
        explicit_indicators = ['going to', 'time for', 'just', 'about to']
        confidence = 0.6  # Base confidence
        
        for indicator in explicit_indicators:
            if indicator in message.lower():
                confidence += 0.1
        
        return min(1.0, confidence)

    def _calculate_time_consistency(self, timestamps: List[datetime]) -> float:
        """Calculate how consistent the timing pattern is."""
        if len(timestamps) < 2:
            return 0.0

        hours = [ts.hour + ts.minute / 60.0 for ts in timestamps]
        variance = sum((h - sum(hours) / len(hours)) ** 2 for h in hours) / len(hours)

        # Convert variance to consistency score (lower variance = higher consistency)
        consistency = max(0.0, 1.0 - variance / 12.0)  # Normalize by 12 hours
        return consistency

    async def _analyze_relationship_sentiment(self, messages: List[ConversationMessage]) -> List[InferredInsight]:
        """Analyze sentiment patterns related to specific relationships."""
        insights = []
        name_sentiments = defaultdict(list)

        # Extract names and associated sentiments
        for msg in messages:
            if not msg.emotion_profile:
                continue

            # Simple name extraction (in production, would use NER)
            words = msg.message.split()
            potential_names = [word.strip('.,!?') for word in words if word[0].isupper() and len(word) > 2]

            for name in potential_names:
                # Skip common non-name words
                if name.lower() not in ['the', 'and', 'but', 'for', 'with', 'this', 'that']:
                    name_sentiments[name].append({
                        'sentiment': msg.emotion_profile.sentiment_polarity,
                        'timestamp': msg.timestamp,
                        'message_id': msg.id
                    })

        # Generate insights for names with sufficient mentions
        for name, sentiments in name_sentiments.items():
            if len(sentiments) >= 3:  # Need multiple mentions
                avg_sentiment = sum(s['sentiment'] for s in sentiments) / len(sentiments)
                sentiment_consistency = 1.0 - (
                    sum(abs(s['sentiment'] - avg_sentiment) for s in sentiments) / len(sentiments)
                )

                if abs(avg_sentiment) > 0.3 and sentiment_consistency > 0.6:
                    insights.append(InferredInsight(
                        insight_type='RelationshipSentiment',
                        confidence_score=sentiment_consistency,
                        timestamp=datetime.now(),
                        data={
                            'name': name,
                            'avg_sentiment': avg_sentiment,
                            'mentions': len(sentiments),
                            'sentiment_consistency': sentiment_consistency,
                            'relationship_type': self._infer_relationship_type(name, messages)
                        },
                        source_message_ids=[s['message_id'] for s in sentiments]
                    ))

        return insights

    async def _analyze_daily_routines(self, messages: List[ConversationMessage]) -> List[InferredInsight]:
        """Analyze daily routine patterns."""
        insights = []
        activity_times = defaultdict(list)

        # Common routine activities
        routine_patterns = {
            'work': re.compile(r'\b(work|office|meeting|job|working)\b', re.IGNORECASE),
            'meal': re.compile(r'\b(breakfast|lunch|dinner|eating|meal)\b', re.IGNORECASE),
            'commute': re.compile(r'\b(commute|driving|train|bus|travel)\b', re.IGNORECASE),
            'social': re.compile(r'\b(friends|party|hangout|social|gathering)\b', re.IGNORECASE)
        }

        for msg in messages:
            for activity, pattern in routine_patterns.items():
                if pattern.search(msg.message):
                    activity_times[activity].append({
                        'timestamp': msg.timestamp,
                        'hour': msg.timestamp.hour,
                        'day_of_week': msg.timestamp.weekday(),
                        'message_id': msg.id
                    })

        # Generate routine insights
        for activity, times in activity_times.items():
            if len(times) >= 5:  # Need sufficient data points
                # Calculate average time and consistency
                avg_hour = sum(t['hour'] for t in times) / len(times)
                hour_variance = sum((t['hour'] - avg_hour) ** 2 for t in times) / len(times)
                consistency = max(0.0, 1.0 - hour_variance / 144)  # Normalize by 12^2

                if consistency > 0.6:  # Only report consistent routines
                    insights.append(InferredInsight(
                        insight_type='DailyRoutine',
                        confidence_score=consistency,
                        timestamp=datetime.now(),
                        data={
                            'activity': activity,
                            'average_hour': avg_hour,
                            'consistency_score': consistency,
                            'frequency': len(times),
                            'weekday_pattern': self._analyze_weekday_pattern(times)
                        },
                        source_message_ids=[t['message_id'] for t in times]
                    ))

        return insights

    async def _analyze_mood_patterns(self, messages: List[ConversationMessage]) -> List[InferredInsight]:
        """Analyze mood patterns over time."""
        insights = []
        mood_data = []

        for msg in messages:
            if msg.emotion_profile:
                mood_data.append({
                    'timestamp': msg.timestamp,
                    'sentiment': msg.emotion_profile.sentiment_polarity,
                    'intensity': msg.emotion_profile.emotion_intensity,
                    'day_of_week': msg.timestamp.weekday(),
                    'hour': msg.timestamp.hour,
                    'message_id': msg.id
                })

        if len(mood_data) >= 10:  # Need sufficient data
            # Analyze weekly mood patterns
            weekday_moods = defaultdict(list)
            for data in mood_data:
                weekday_moods[data['day_of_week']].append(data['sentiment'])

            # Find significant mood differences by day
            avg_weekday_moods = {day: sum(moods) / len(moods) for day, moods in weekday_moods.items() if len(moods) >= 2}

            if len(avg_weekday_moods) >= 5:  # Have data for most days
                mood_variance = max(avg_weekday_moods.values()) - min(avg_weekday_moods.values())

                if mood_variance > 0.4:  # Significant mood variation
                    insights.append(InferredInsight(
                        insight_type='WeeklyMoodPattern',
                        confidence_score=0.7,
                        timestamp=datetime.now(),
                        data={
                            'weekday_moods': avg_weekday_moods,
                            'mood_variance': mood_variance,
                            'best_day': max(avg_weekday_moods.items(), key=lambda x: x[1]),
                            'worst_day': min(avg_weekday_moods.items(), key=lambda x: x[1])
                        },
                        source_message_ids=[data['message_id'] for data in mood_data]
                    ))

        return insights

    def _infer_relationship_type(self, name: str, messages: List[ConversationMessage]) -> str:
        """Infer the type of relationship based on context."""
        context_words = []

        for msg in messages:
            if name.lower() in msg.message.lower():
                context_words.extend(msg.message.lower().split())

        # Check for relationship type indicators
        for rel_type, indicators in self.relationship_indicators.items():
            if any(indicator in context_words for indicator in indicators):
                return rel_type

        return 'unknown'

    def _analyze_weekday_pattern(self, times: List[Dict]) -> Dict[str, float]:
        """Analyze activity patterns by day of week."""
        weekday_counts = defaultdict(int)
        for time_data in times:
            weekday_counts[time_data['day_of_week']] += 1

        total_occurrences = sum(weekday_counts.values())
        weekday_percentages = {
            str(day): count / total_occurrences
            for day, count in weekday_counts.items()
        }

        return weekday_percentages
