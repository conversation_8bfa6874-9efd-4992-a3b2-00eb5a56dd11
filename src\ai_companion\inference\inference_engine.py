"""
Inference Engine for the Aura AI Companion.
Analyzes conversation transcripts to extract implicit facts and patterns.
This is the heart of the "Observant Friend" feature.
"""
import re
from datetime import datetime, time
from typing import List, Dict, Optional

from ..models.data_models import InferredInsight, ConversationMessage, EmotionProfile


class InferenceEngine:
    """
    The core data-gathering engine for the Implicit Knowledge Graph.
    Analyzes conversation transcripts to extract implicit facts and patterns
    without invasive questioning. This is the heart of the "Observant Friend" feature.
    """

    def __init__(self):
        # Pre-compiled regex for efficiency
        self.sleep_patterns = {
            "goodnight": re.compile(r'\b(good night|night|gn|sleep well)\b', re.IGNORECASE),
            "wake_up": re.compile(r'\b(good morning|morning|just woke up)\b', re.IGNORECASE)
        }
        self.exercise_keywords = {'run', 'running', 'gym', 'workout', 'exercise', 'walk'}

        # Extended stress detection keywords
        self.stress_keywords = {
            'stress', 'stressed', 'anxious', 'anxiety', 'overwhelmed', 'burnt out',
            'burnout', 'frustrated', 'frustration', 'pressure', 'worried', 'worry'
        }
        print("InferenceEngine initialized.")

    def infer_from_conversation(self, transcript: List[Dict]) -> Dict:
        """
        Analyzes a full conversation transcript and extracts a dictionary of inferred insights.
        A transcript is a list of dicts: [{'timestamp': datetime, 'sender': 'user', 'message': str, 'emotion_profile': EmotionProfile}]
        """
        inferred_data = {
            "potential_sleep_time": None,
            "potential_wake_time": None,
            "stress_coping_mechanisms": []
        }

        # STEP 1: Infer Sleep/Wake Times.
        # Iterate through the transcript.
        # If a message matches the 'goodnight' regex, record its timestamp as `potential_sleep_time`.
        # If a message matches the 'wake_up' regex, record its timestamp as `potential_wake_time`.
        # If multiple matches exist, use the latest one for each.

        for message_dict in transcript:
            if message_dict['sender'] != 'user':
                continue

            message_text = message_dict['message']
            timestamp = message_dict['timestamp']

            # Check for goodnight patterns
            if self.sleep_patterns["goodnight"].search(message_text):
                inferred_data["potential_sleep_time"] = timestamp

            # Check for wake up patterns
            if self.sleep_patterns["wake_up"].search(message_text):
                inferred_data["potential_wake_time"] = timestamp

        # STEP 2: Infer Coping Mechanisms for Stress.
        # Find a message where the user's emotion profile has 'stress' or 'anxiety' as a primary emotion with high intensity (> 0.7).
        # Once found, search in the *subsequent* messages from the user.
        # If a subsequent message contains a keyword from `self.exercise_keywords` AND has a positive sentiment (e.g., primary emotion is 'happy', 'calm', or 'relieved'):
        #   - We can infer a positive coping mechanism.
        #   - Append a dictionary to `stress_coping_mechanisms`:
        #     {'trigger': 'work_stress', 'mechanism': 'exercise', 'outcome': 'positive'}

        for i, message_dict in enumerate(transcript):
            if message_dict['sender'] != 'user' or not message_dict.get('emotion_profile'):
                continue

            emotion_profile = message_dict['emotion_profile']

            # Check for high stress/anxiety
            is_stressed = (
                emotion_profile.emotion_intensity > 0.7 and
                emotion_profile.primary_emotion.value in ['anger', 'fear', 'sadness'] and
                emotion_profile.sentiment_polarity < -0.3
            ) or any(keyword in message_dict['message'].lower() for keyword in self.stress_keywords)

            if is_stressed:
                # Search subsequent messages for coping mechanisms
                for j in range(i + 1, min(i + 10, len(transcript))):  # Look at next 10 messages
                    subsequent_msg = transcript[j]
                    if subsequent_msg['sender'] != 'user' or not subsequent_msg.get('emotion_profile'):
                        continue

                    # Check for exercise keywords
                    if any(keyword in subsequent_msg['message'].lower() for keyword in self.exercise_keywords):
                        # Check if mood improved (positive sentiment)
                        if (subsequent_msg['emotion_profile'].sentiment_polarity > 0.3 or
                            subsequent_msg['emotion_profile'].primary_emotion.value in ['joy', 'neutral']):

                            inferred_data["stress_coping_mechanisms"].append({
                                'trigger': 'work_stress',
                                'mechanism': 'exercise',
                                'outcome': 'positive'
                            })
                            break  # Found a coping mechanism, move to next stress event

        # STEP 3: Return the Dictionary of Inferred Data.
        # This dictionary will be used by another service to update the IKG.
        return inferred_data

    def generate_report_snippet(self, inferred_knowledge: Dict) -> str:
        """
        Generates a human-readable snippet for the therapist report based on aggregated knowledge.
        `inferred_knowledge` is the compiled data from the IKG.
        """
        # STEP 1: Generate Sleep Analysis Snippet.
        # This demonstrates how the inferred data is translated into clinical language.
        # Check if `avg_sleep_time` exists in the knowledge dict.
        # If so, create a sentence like:
        # "Sleep Analysis: User shows a pattern of late sleep times, typically around {avg_sleep_time}. This may contribute to reported fatigue."

        report_snippets = []

        if 'avg_sleep_time' in inferred_knowledge:
            avg_sleep_time = inferred_knowledge['avg_sleep_time']
            if isinstance(avg_sleep_time, (int, float)):
                if avg_sleep_time >= 24:
                    avg_sleep_time = avg_sleep_time - 24  # Handle 24+ hour format
                sleep_snippet = f"Sleep Analysis: User shows a pattern of late sleep times, typically around {avg_sleep_time:.1f}:00. This may contribute to reported fatigue."
            else:
                sleep_snippet = f"Sleep Analysis: User shows a pattern of late sleep times, typically around {avg_sleep_time}. This may contribute to reported fatigue."
            report_snippets.append(sleep_snippet)

        # STEP 2: Generate Coping Mechanism Snippet.
        # Check for coping mechanisms in the knowledge dict.
        # If found, create a sentence like:
        # "Coping Strategy Analysis: A positive correlation is observed between reported stress and subsequent physical activity (e.g., running), suggesting a healthy coping mechanism."

        if 'coping_mechanisms' in inferred_knowledge:
            coping_mechanisms = inferred_knowledge['coping_mechanisms']
            if coping_mechanisms and len(coping_mechanisms) > 0:
                # Check if exercise is a common coping mechanism
                exercise_coping = any(
                    mech.get('mechanism') == 'exercise' and mech.get('outcome') == 'positive'
                    for mech in coping_mechanisms
                )
                if exercise_coping:
                    coping_snippet = "Coping Strategy Analysis: A positive correlation is observed between reported stress and subsequent physical activity (e.g., running), suggesting a healthy coping mechanism."
                    report_snippets.append(coping_snippet)

        # STEP 3: Combine and return the snippets.
        if report_snippets:
            report_text = " ".join(report_snippets)
        else:
            report_text = "Insufficient data for comprehensive analysis. Continued observation recommended."

        return report_text
