#!/usr/bin/env python3
"""
Demo script to showcase the Gradio interface for Aura AI Companion.
This script demonstrates the web interface capabilities.
"""
import sys
import os
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_requirements():
    """Check if all requirements are installed."""
    print("🔍 Checking requirements...")
    
    missing_packages = []
    
    try:
        import gradio
        print(f"✅ Gradio {gradio.__version__} installed")
    except ImportError:
        missing_packages.append("gradio>=4.0.0")
    
    try:
        import google.generativeai
        print("✅ Google Generative AI installed")
    except ImportError:
        missing_packages.append("google-generativeai>=0.3.0")
    
    try:
        from dotenv import load_dotenv
        print("✅ Python-dotenv installed")
    except ImportError:
        missing_packages.append("python-dotenv>=1.0.0")
    
    try:
        from ai_companion import create_aura_companion
        print("✅ Aura AI Companion available")
    except ImportError as e:
        print(f"❌ Aura AI Companion import failed: {e}")
        return False
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_api_key():
    """Check API key configuration."""
    print("\n🔑 Checking API key configuration...")
    
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv("GOOGLE_API_KEY")
        if api_key:
            print("✅ Google API key found - Full LLM capabilities enabled")
            return True
        else:
            print("⚠️  No Google API key found")
            print("   Aura will use fallback response templates")
            print("   For full capabilities, add GOOGLE_API_KEY to .env file")
            return False
    except Exception as e:
        print(f"❌ Error checking API key: {e}")
        return False

def demo_interface_features():
    """Demonstrate the interface features."""
    print("\n🌟 Aura AI Companion - Gradio Interface Demo")
    print("=" * 50)
    
    print("""
🌐 **Web Interface Features:**

💬 **Interactive Chat:**
   • Natural conversations with Aura as a human friend
   • Real-time empathetic responses
   • Context-aware dialogue

📊 **Live Analysis:**
   • Emotion detection (joy, sadness, anger, fear, etc.)
   • Sentiment analysis (-1.0 to ****)
   • Crisis signal detection
   • Risk level assessment

🧠 **Behavioral Insights:**
   • Sleep pattern detection
   • Stress coping mechanism analysis
   • Relationship sentiment tracking
   • Daily routine recognition
   • Weekly mood patterns

🔒 **Privacy Controls:**
   • Export all your data (GDPR compliance)
   • Delete all data with one click
   • Full transparency about data usage
   • Local processing (no external data sharing)

📱 **User Experience:**
   • Mobile-friendly responsive design
   • Clean, intuitive interface
   • Real-time updates
   • Fast response times
""")

def demo_sample_conversations():
    """Show sample conversations that work well with Aura."""
    print("\n💬 **Sample Conversations to Try:**")
    print("-" * 40)
    
    samples = [
        ("Casual Check-in", "Hey Aura, how's your day going?"),
        ("Work Stress", "I'm feeling really overwhelmed with work deadlines"),
        ("Relationship Issue", "I had an argument with my friend and I'm not sure what to do"),
        ("Sleep Problems", "I've been staying up too late and feeling tired all day"),
        ("Positive Sharing", "I just finished a great workout and feel amazing!"),
        ("Seeking Advice", "I'm trying to build better habits but keep procrastinating"),
    ]
    
    for category, message in samples:
        print(f"📝 **{category}:**")
        print(f"   \"{message}\"")
        print()

def launch_interface():
    """Launch the Gradio interface."""
    print("\n🚀 Launching Aura AI Companion Web Interface...")
    print("-" * 50)
    
    print("The interface will open at: http://localhost:7860")
    print("Features available:")
    print("  • Real-time chat with emotion analysis")
    print("  • Behavioral insights dashboard")
    print("  • Privacy controls (export/delete data)")
    print("  • Mobile-friendly design")
    print()
    print("Press Ctrl+C to stop the server when you're done.")
    print()
    
    # Import and run the Gradio app
    try:
        import gradio_app
        print("✅ Gradio interface launched successfully!")
    except KeyboardInterrupt:
        print("\n👋 Interface stopped. Thank you for trying Aura!")
    except Exception as e:
        print(f"❌ Error launching interface: {e}")
        print("Make sure gradio_app.py is in the same directory.")

def main():
    """Main demo function."""
    print("🌟 Aura AI Companion - Gradio Interface Demo")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please install missing packages.")
        return False
    
    # Check API key
    has_api_key = check_api_key()
    
    # Show interface features
    demo_interface_features()
    
    # Show sample conversations
    demo_sample_conversations()
    
    # Ask user if they want to launch
    print("🚀 Ready to launch the web interface!")
    
    if has_api_key:
        print("✅ Full LLM capabilities will be available.")
    else:
        print("⚠️  Fallback mode will be used (still fully functional).")
    
    try:
        response = input("\nLaunch the Gradio interface? (y/n): ").lower().strip()
        if response in ['y', 'yes', '']:
            launch_interface()
        else:
            print("👋 Demo completed. Run 'python gradio_app.py' when ready!")
    except KeyboardInterrupt:
        print("\n👋 Demo cancelled. Run 'python gradio_app.py' when ready!")
    
    return True

if __name__ == "__main__":
    main()
