"""
Basic usage example for the Aura AI Companion.
Demonstrates core functionality including conversation, insights, and reporting.
"""
import asyncio
import json
from datetime import datetime

# Import the Aura AI Companion
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.ai_companion import create_aura_companion


async def main():
    """Demonstrate basic usage of the Aura AI Companion."""
    
    print("🌟 Aura AI Companion - Basic Usage Example")
    print("=" * 50)
    
    # Create the companion instance
    aura = create_aura_companion()
    
    # Create a user profile
    user_id = "demo_user_001"
    print(f"\n📝 Creating user profile for {user_id}...")
    
    user_profile = await aura.create_user(
        user_id=user_id,
        preferences={
            "response_style": "supportive",
            "crisis_intervention": True,
            "insights_enabled": True
        },
        privacy_settings={
            "allow_insights_generation": True,
            "allow_therapist_reports": False,
            "data_retention_days": 365
        }
    )
    print(f"✅ User profile created: {user_profile['user_id']}")
    
    # Simulate a conversation
    print(f"\n💬 Starting conversation simulation...")
    
    # Sample messages representing different emotional states
    sample_messages = [
        "Hi there! I'm feeling pretty good today, just finished a great workout.",
        "I've been having trouble sleeping lately, going to bed really late.",
        "Work has been super stressful this week, feeling overwhelmed.",
        "Went for a run after that stressful day and felt much better!",
        "Had a great conversation with my friend Alex, always makes me smile.",
        "Feeling a bit down today, not sure why.",
        "Good morning! Woke up early and feeling refreshed.",
        "My mom called yesterday, we had a nice long chat.",
        "Been feeling anxious about the presentation tomorrow.",
        "Just finished the presentation and it went really well!"
    ]
    
    # Process each message
    for i, message in enumerate(sample_messages, 1):
        print(f"\n--- Message {i} ---")
        print(f"User: {message}")
        
        # Process the message through Aura
        response_data = await aura.chat(user_id, message)
        
        print(f"Aura: {response_data['response']}")
        print(f"Emotion: {response_data['emotion_analysis']['primary_emotion']} "
              f"(intensity: {response_data['emotion_analysis']['emotion_intensity']:.2f})")
        print(f"Risk Level: {response_data['risk_level']}")
        print(f"Processing Time: {response_data['processing_time']:.3f}s")
        
        # Add a small delay to simulate real conversation timing
        await asyncio.sleep(0.1)
    
    # Get user insights
    print(f"\n📊 Generating user insights...")
    insights = await aura.get_user_insights(user_id)
    
    print(f"Conversation Stats:")
    print(f"- Total messages: {insights['conversation_stats']['total_messages']}")
    print(f"- User messages: {insights['conversation_stats']['user_messages']}")
    print(f"- Conversation span: {insights['conversation_stats']['conversation_span_days']} days")
    
    if insights['emotional_trends']:
        print(f"\nEmotional Trends:")
        print(f"- Average sentiment: {insights['emotional_trends'].get('avg_sentiment', 0):.2f}")
        print(f"- Emotional volatility: {insights['emotional_trends'].get('emotional_volatility', 0):.2f}")
    
    if insights['recent_insights']:
        print(f"\nRecent Insights Detected:")
        for insight in insights['recent_insights'][:3]:  # Show first 3
            print(f"- {insight['insight_type']}: {insight['confidence_score']:.2f} confidence")
    
    # Get conversation history
    print(f"\n📜 Retrieving conversation history...")
    history = await aura.get_conversation_history(user_id, limit=5)
    
    print(f"Last 5 messages:")
    for msg in history[-5:]:
        sender = "User" if msg['sender'] == 'user' else "Aura"
        timestamp = datetime.fromisoformat(msg['timestamp']).strftime("%H:%M:%S")
        print(f"[{timestamp}] {sender}: {msg['message'][:50]}...")
    
    # Demonstrate privacy features
    print(f"\n🔒 Privacy and Data Export Demo...")
    
    # Export user data (for GDPR compliance)
    exported_data = await aura.export_user_data(user_id)
    
    print(f"Data export summary:")
    print(f"- User profile: {'✓' if exported_data['user_profile'] else '✗'}")
    print(f"- Conversation history: {len(exported_data['conversation_history'])} messages")
    print(f"- Insights summary: {'✓' if exported_data['insights_summary'] else '✗'}")
    print(f"- Export timestamp: {exported_data['export_timestamp']}")
    
    # Demonstrate crisis detection
    print(f"\n🚨 Crisis Detection Demo...")
    
    crisis_message = "I'm feeling really hopeless and don't see the point in anything anymore"
    print(f"User: {crisis_message}")
    
    crisis_response = await aura.chat(user_id, crisis_message)
    
    print(f"Aura: {crisis_response['response']}")
    print(f"Risk Level: {crisis_response['risk_level']}")
    print(f"Crisis Signal Detected: {crisis_response['emotion_analysis']['is_crisis_signal']}")
    
    # Show ECoT process for crisis response
    if 'ecot_process' in crisis_response:
        ecot = crisis_response['ecot_process']
        print(f"\nEmotional Chain-of-Thought Process:")
        for step in ecot['steps']:
            print(f"- {step['step_type']}: {step['confidence']:.2f} confidence")
    
    print(f"\n✨ Demo completed successfully!")
    print(f"The Aura AI Companion demonstrated:")
    print(f"  ✓ Real-time emotion analysis")
    print(f"  ✓ Behavioral pattern recognition")
    print(f"  ✓ Crisis detection and intervention")
    print(f"  ✓ Privacy-preserving insights generation")
    print(f"  ✓ Emotional Chain-of-Thought reasoning")
    print(f"  ✓ Data export for privacy compliance")


async def demonstrate_therapist_report():
    """Demonstrate therapist report generation (requires user consent)."""
    
    print(f"\n📋 Therapist Report Demo")
    print("=" * 30)
    
    aura = create_aura_companion()
    user_id = "demo_user_002"
    
    # Create user with therapist report consent
    await aura.create_user(
        user_id=user_id,
        privacy_settings={
            "allow_therapist_reports": True,
            "allow_insights_generation": True
        }
    )
    
    # Simulate some conversation data
    messages = [
        "I've been having trouble sleeping, usually go to bed around 2 AM",
        "Work stress is really getting to me lately",
        "Went for a walk today and felt a bit better",
        "Had coffee with Sarah, she's such a good friend",
        "Feeling overwhelmed again, might need to talk to someone",
        "Good night, finally going to bed at a reasonable time",
        "Morning! Actually got 8 hours of sleep for once"
    ]
    
    for message in messages:
        await aura.chat(user_id, message)
        await asyncio.sleep(0.05)  # Small delay
    
    # Generate therapist report
    print(f"Generating privacy-preserving therapist report...")
    
    try:
        report = await aura.generate_therapist_report(user_id, time_period_days=7)
        
        print(f"\n📊 Report Generated Successfully")
        print(f"Report ID: {report['metadata']['report_id']}")
        print(f"Privacy Techniques: {', '.join(report['metadata']['privacy_techniques_applied'])}")
        
        print(f"\n--- Report Content Preview ---")
        # Show first 500 characters of the report
        content_preview = report['report_content'][:500] + "..." if len(report['report_content']) > 500 else report['report_content']
        print(content_preview)
        
        print(f"\n🔒 Privacy Summary:")
        for technique in report['privacy_summary']['privacy_techniques']:
            print(f"  ✓ {technique}")
        
        print(f"\n⚠️  Clinical Disclaimers:")
        for disclaimer in report['clinical_disclaimers'][:2]:  # Show first 2
            print(f"  • {disclaimer}")
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")


if __name__ == "__main__":
    # Run the basic demo
    asyncio.run(main())
    
    # Uncomment to run therapist report demo
    # asyncio.run(demonstrate_therapist_report())
