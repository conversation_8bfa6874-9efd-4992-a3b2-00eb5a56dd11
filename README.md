# Aura AI Companion 🌟

An empathetic AI assistant with deep emotional understanding, behavioral pattern recognition, and privacy-preserving insights generation.

## Overview

Aura is a sophisticated AI companion that provides emotional support through advanced emotion analysis, behavioral pattern recognition, and privacy-preserving insights generation. It implements cutting-edge techniques including Emotional Chain-of-Thought (ECoT) reasoning, Implicit Knowledge Graphs (IKG), and multi-layered crisis intervention systems.

## Key Features

### 🧠 Emotional Intelligence
- **Real-time Emotion Analysis**: Advanced sentiment analysis with emotion classification, intensity measurement, and arousal level detection
- **Crisis Detection**: Multi-layered crisis detection with immediate intervention protocols
- **Emotional Chain-of-Thought (ECoT)**: Internal reasoning process that considers emotional context before generating responses

### 🔍 Pattern Recognition
- **Behavioral Insights**: Automatic detection of sleep patterns, coping mechanisms, relationship dynamics, and daily routines
- **Implicit Knowledge Graph**: Long-term memory system that builds understanding of user patterns over time
- **Trend Analysis**: Longitudinal analysis of emotional and behavioral trends

### 🔒 Privacy & Safety
- **Differential Privacy**: Mathematical privacy guarantees for all aggregated data
- **Data Anonymization**: Comprehensive anonymization of personally identifiable information
- **Crisis Intervention**: Professional-grade crisis detection with appropriate resource referrals
- **Consent Management**: Granular consent controls for different types of data processing

### 📊 Professional Integration
- **Therapist Reports**: Privacy-preserving, synthesized reports for mental health professionals
- **GDPR Compliance**: Full data export and deletion capabilities
- **Audit Logging**: Comprehensive privacy audit trails

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Aura AI Companion                        │
├─────────────────────────────────────────────────────────────┤
│  ConversationService (Main Orchestrator)                   │
├─────────────────────────────────────────────────────────────┤
│  EmotionAnalysisService  │  InferenceEngine                 │
│  - Real-time emotion     │  - Pattern recognition           │
│  - Crisis detection      │  - Behavioral insights           │
├─────────────────────────────────────────────────────────────┤
│  ECoTService            │  KnowledgeGraphService            │
│  - Emotional reasoning  │  - Long-term memory              │
│  - Response generation  │  - Context retrieval             │
├─────────────────────────────────────────────────────────────┤
│  CrisisService          │  ReportGenerator                 │
│  - Risk assessment      │  - Privacy-preserving reports    │
│  - Intervention         │  - Professional insights         │
├─────────────────────────────────────────────────────────────┤
│                    Privacy Layer                            │
│  - Differential Privacy - Anonymization - Consent Mgmt     │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/aura-ai-companion.git
cd aura-ai-companion

# Install dependencies
pip install -r requirements.txt

# Install the package
pip install -e .
```

### 🌐 Web Interface (Recommended)

**Quick Start with Gradio Interface:**

```bash
# Launch the web interface
python gradio_app.py

# Or use the setup script
python run_gradio_app.py
```

This opens an interactive web interface at `http://localhost:7860` with:
- 💬 **Real-time chat** with Aura
- 📊 **Live emotion analysis**
- 🧠 **Behavioral insights** visualization
- 🔒 **Privacy controls** (export/delete data)

See [GRADIO_SETUP.md](GRADIO_SETUP.md) for detailed setup instructions.

### 🐍 Python API Usage

```python
import asyncio
from src.ai_companion import create_aura_companion

async def main():
    # Create the companion
    aura = create_aura_companion()

    # Create a user
    user_id = "user_123"
    await aura.create_user(user_id)

    # Have a conversation
    response = await aura.chat(user_id, "I'm feeling stressed about work today")
    print(f"Aura: {response['response']}")
    print(f"Emotion detected: {response['emotion_analysis']['primary_emotion']}")
    print(f"Risk level: {response['risk_level']}")

    # Get insights
    insights = await aura.get_user_insights(user_id)
    print(f"Patterns detected: {len(insights['recent_insights'])}")

asyncio.run(main())
```

### Advanced Features

```python
# Generate therapist report (requires consent)
report = await aura.generate_therapist_report(user_id, time_period_days=30)

# Export user data (GDPR compliance)
user_data = await aura.export_user_data(user_id)

# Clear user data
await aura.clear_user_data(user_id)
```

## 🌐 Web Interface Features

### Interactive Chat
- **💬 Real-time Conversations:** Natural, empathetic responses as a human friend
- **📊 Live Emotion Analysis:** See emotional state, sentiment, and intensity in real-time
- **🚨 Crisis Detection:** Automatic detection with appropriate support resources
- **📱 Mobile-Friendly:** Responsive design works on all devices

### Privacy & Insights Dashboard
- **🧠 Behavioral Insights:** View detected patterns (sleep, coping mechanisms, routines)
- **📁 Data Export:** Download all your data for privacy compliance (GDPR)
- **🗑️ Data Deletion:** Clear all personal data with one click
- **🔒 Privacy Controls:** Full transparency and control over your information

### User Experience
- **🎨 Clean Interface:** Intuitive design focused on conversation
- **⚡ Fast Responses:** Optimized for quick, natural interactions
- **🔄 Real-time Updates:** Live emotion analysis and risk assessment
- **📈 Progress Tracking:** See insights develop over time

## Core Components

### EmotionAnalysisService
Real-time emotional analysis with:
- Multi-dimensional emotion classification
- Crisis signal detection
- Sentiment polarity and arousal measurement
- Confidence scoring

### InferenceEngine
Pattern recognition system that detects:
- Sleep and wake patterns
- Stress coping mechanisms
- Relationship sentiment patterns
- Daily routine analysis
- Weekly mood patterns

### ECoTService (Emotional Chain-of-Thought)
Internal reasoning process with steps:
1. Emotional analysis of user input
2. Context retrieval from knowledge graph
3. Emotional state assessment
4. Response strategy planning
5. Response generation
6. Response validation

### KnowledgeGraphService
Long-term memory system featuring:
- Node-based knowledge representation
- Relationship mapping
- Vector similarity search
- Context expansion through graph traversal

### CrisisService
Multi-layered crisis intervention:
- Pattern-based crisis detection
- Risk level assessment
- Resource referral
- Emergency alert protocols

## Privacy & Ethics

Aura implements multiple privacy-preserving techniques:

### Differential Privacy
- Mathematical privacy guarantees
- Calibrated noise injection
- Configurable privacy parameters (ε)

### Data Minimization
- Purpose-limited data collection
- Automatic data retention policies
- Granular consent management

### Anonymization
- Personal identifier removal
- Consistent pseudonymization
- K-anonymity for datasets

### Transparency
- Complete audit logging
- Privacy impact assessments
- User data export capabilities

## Testing

Run the test suite:

```bash
# Run all tests
pytest tests/ -v

# Run specific test categories
pytest tests/test_emotion_analysis.py -v
pytest tests/test_inference_engine.py -v
pytest tests/test_privacy.py -v

# Run with coverage
pytest tests/ --cov=src/ai_companion --cov-report=html
```

## Examples

See the `examples/` directory for comprehensive usage examples:

- `basic_usage.py`: Core functionality demonstration
- `crisis_intervention.py`: Crisis detection and intervention
- `privacy_features.py`: Privacy and data protection features
- `therapist_integration.py`: Professional report generation

## Configuration

### Privacy Settings
```python
privacy_settings = {
    "allow_insights_generation": True,
    "allow_therapist_reports": False,
    "data_retention_days": 365,
    "differential_privacy_epsilon": 1.0
}
```

### User Preferences
```python
preferences = {
    "response_style": "supportive",  # supportive, clinical, casual
    "crisis_intervention": True,
    "insights_enabled": True,
    "max_response_length": 200
}
```

## API Reference

### Main Interface

#### `AuraCompanion.chat(user_id, message)`
Process a user message and return empathetic response.

**Returns:**
```python
{
    'response': str,
    'emotion_analysis': EmotionProfile,
    'risk_level': str,
    'ecot_process': ECoTProcess,
    'processing_time': float
}
```

#### `AuraCompanion.get_user_insights(user_id)`
Get behavioral insights and patterns for a user.

#### `AuraCompanion.generate_therapist_report(user_id, time_period_days)`
Generate privacy-preserving professional report.

## Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Install pre-commit hooks
pre-commit install

# Run linting
flake8 src/ tests/
black src/ tests/

# Run type checking
mypy src/
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Ethical Considerations

Aura is designed with mental health ethics in mind:

- **Non-diagnostic**: Never provides clinical diagnoses
- **Professional referral**: Encourages professional help when appropriate
- **Crisis intervention**: Immediate resource provision for crisis situations
- **Privacy-first**: User privacy is paramount in all operations
- **Transparency**: Clear about AI limitations and capabilities

## Support

- **Documentation**: [docs.aura-ai.com](https://docs.aura-ai.com)
- **Issues**: [GitHub Issues](https://github.com/your-org/aura-ai-companion/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/aura-ai-companion/discussions)
- **Email**: <EMAIL>

## Acknowledgments

- Mental health professionals who provided guidance on ethical AI design
- Privacy researchers who contributed to differential privacy implementation
- Open source community for foundational libraries and tools

---

**⚠️ Important Notice**: Aura is an AI assistant designed to provide emotional support and insights. It is not a replacement for professional mental health care. If you're experiencing a mental health crisis, please contact a qualified mental health professional or emergency services immediately.
