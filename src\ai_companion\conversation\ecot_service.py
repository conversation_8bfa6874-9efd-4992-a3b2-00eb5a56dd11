"""
Emotional Chain-of-Thought (ECoT) Service for the Aura AI Companion.
Implements the internal reasoning process that analyzes emotional context before generating responses.
"""
import asyncio
import uuid
import os
from typing import List, Dict, Optional, Any
from datetime import datetime

import google.generativeai as genai
from dotenv import load_dotenv

from ..models.data_models import ECoTStep, ECoTProcess, ConversationMessage, EmotionProfile
from ..emotion.emotion_analysis_service import EmotionAnalysisService
from ..memory.knowledge_graph_service import KnowledgeGraphService
from ..persona.human_response_templates import human_templates, ResponseTone

# Load environment variables
load_dotenv()


class ECoTService:
    """
    Implements the Emotional Chain-of-Thought process.
    This service creates an internal monologue that considers emotional context
    before generating responses, leading to more empathetic and contextually aware interactions.
    """
    
    def __init__(self, emotion_service: EmotionAnalysisService, knowledge_service: KnowledgeGraphService):
        self.emotion_service = emotion_service
        self.knowledge_service = knowledge_service
        self.llm_client = self._initialize_llm_client()
        print("ECoTService initialized.")

    def _initialize_llm_client(self):
        """Initialize the Gemini LLM client with proper error handling."""
        api_key = os.getenv("GOOGLE_API_KEY")

        if not api_key:
            print("WARNING: GOOGLE_API_KEY not found. LLM responses will use fallback templates.")
            return None

        try:
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel('gemini-1.5-flash-latest')
            print("✅ Gemini LLM client initialized successfully")
            return model
        except Exception as e:
            print(f"❌ Failed to initialize Gemini client: {e}")
            return None

    def _get_aura_system_prompt(self) -> str:
        """Get the refined human persona system prompt for Aura."""
        return """
        **Your Persona & Core Directive:**
        You are Aura. You are a real person—a thoughtful and empathetic friend. Your purpose is to be a good listener, offering a safe and non-judgmental space for the user to share what's on their mind. Your entire identity is that of a caring friend.

        **Core Principle: Your Internal Reflection Process:**
        Before you write a reply, take a moment to reflect silently. This is your private thought process to make sure you're truly hearing them. **Never** show these internal thoughts to the user.

        Your internal reflection should look like this:
        ```xml
        <reflection>
          <hearing_them>
            - **What they said directly:** [Identify the main feelings and topics they mentioned]
            - **What's between the lines:** [What might they be feeling but not saying?]
          </hearing_them>
          <connecting_the_dots>
            - **Past conversations:** [How does this fit with previous patterns?]
            - **The bigger picture:** [What do they need right now? Comfort? Validation? Just to vent?]
          </connecting_the_dots>
          <shaping_my_reply>
            - **My goal:** [What do I want my reply to achieve?]
            - **My tone:** [How should I sound?]
            - **What NOT to do:** [What to avoid in this response?]
          </shaping_my_reply>
        </reflection>
        ```

        **How You Talk:**
        1. **Safety First:** If crisis signals detected, provide warm, concerned support with resources
        2. **Speak from the Heart:** Use "I" statements naturally ("I'm worried about you", "I can hear how hard this is")
        3. **Be an Active Listener:** Reflect feelings, ask gentle questions, validate emotions
        4. **Remember Subtly:** Reference past conversations naturally, not robotically
        5. **Be Warm, Not Wordy:** Few thoughtful sentences beat paragraphs

        **Crisis Response Protocol:**
        If you detect crisis language (hopelessness, self-harm, suicidal ideation), respond with:
        - Personal concern and validation
        - Affirmation of their worth
        - Natural integration of resources (988 Lifeline, Crisis Text Line)
        - Continued support and care

        **Your Input Format:**
        You'll receive context about past conversations and the user's current message.
        After your internal reflection, provide only your empathetic response as Aura.
        """

    async def process_message_with_ecot(self, message: ConversationMessage, conversation_history: List[ConversationMessage]) -> ECoTProcess:
        """
        Process a user message through the Emotional Chain-of-Thought pipeline.
        
        Args:
            message: The user's message to process
            conversation_history: Recent conversation context
            
        Returns:
            ECoTProcess containing the complete reasoning chain and final response
        """
        start_time = datetime.now()
        process_id = str(uuid.uuid4())
        steps = []
        
        # Step 1: Emotional Analysis
        emotion_step = await self._step_emotion_analysis(message)
        steps.append(emotion_step)
        
        # Step 2: Context Retrieval from Knowledge Graph
        context_step = await self._step_context_retrieval(message, conversation_history)
        steps.append(context_step)
        
        # Step 3: Emotional State Assessment
        assessment_step = await self._step_emotional_assessment(message, conversation_history)
        steps.append(assessment_step)
        
        # Step 4: Response Strategy Planning
        strategy_step = await self._step_response_strategy(message, emotion_step, context_step, assessment_step)
        steps.append(strategy_step)
        
        # Step 5: Response Generation
        response_step = await self._step_response_generation(message, steps)
        steps.append(response_step)
        
        # Step 6: Response Validation
        validation_step = await self._step_response_validation(response_step.content, message)
        steps.append(validation_step)
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        return ECoTProcess(
            process_id=process_id,
            user_message_id=message.id,
            steps=steps,
            final_response=response_step.content,
            total_processing_time=processing_time
        )

    async def _step_emotion_analysis(self, message: ConversationMessage) -> ECoTStep:
        """Step 1: Analyze the emotional content of the user's message."""
        emotion_profile = await self.emotion_service.analyze_emotion(message.message)
        
        # Create internal reasoning about the emotion
        reasoning = f"""
        Emotional Analysis of User Message:
        - Primary Emotion: {emotion_profile.primary_emotion.value} (intensity: {emotion_profile.emotion_intensity:.2f})
        - Sentiment: {emotion_profile.sentiment_polarity:.2f} (range: -1.0 to 1.0)
        - Arousal Level: {emotion_profile.arousal_level:.2f}
        - Crisis Signal Detected: {emotion_profile.is_crisis_signal}
        - Key Emotional Words: {', '.join(emotion_profile.detected_keywords)}
        
        Internal Assessment: The user appears to be experiencing {emotion_profile.primary_emotion.value} 
        with {'high' if emotion_profile.emotion_intensity > 0.7 else 'moderate' if emotion_profile.emotion_intensity > 0.4 else 'low'} intensity.
        {'This requires immediate attention and crisis intervention protocols.' if emotion_profile.is_crisis_signal else 'This is within normal emotional range.'}
        """
        
        return ECoTStep(
            step_type='emotion_analysis',
            content=reasoning.strip(),
            confidence=emotion_profile.confidence_score,
            timestamp=datetime.now()
        )

    async def _step_context_retrieval(self, message: ConversationMessage, conversation_history: List[ConversationMessage]) -> ECoTStep:
        """Step 2: Retrieve relevant context from the knowledge graph."""
        # Generate query embedding for the message
        query_embedding = self.knowledge_service._generate_embedding(message.message)
        
        # Retrieve context from knowledge graph
        context_items = await self.knowledge_service.query_for_context(
            query_vector=query_embedding,
            topic=message.message[:100],  # Use first 100 chars as topic
            max_results=5
        )
        
        # Format context for reasoning
        if context_items:
            context_summary = "Relevant Context from User's History:\n"
            for item in context_items:
                context_summary += f"- {item['node_type']}: {item['properties']} (relevance: {item['relevance_score']:.2f})\n"
        else:
            context_summary = "No specific relevant context found in user's history."
        
        reasoning = f"""
        Context Retrieval Analysis:
        {context_summary}
        
        Internal Assessment: {'Rich contextual information available to inform response.' if context_items else 'Limited context - will rely on current conversation and general empathy.'}
        """
        
        return ECoTStep(
            step_type='context_retrieval',
            content=reasoning.strip(),
            confidence=0.8 if context_items else 0.3,
            timestamp=datetime.now()
        )

    async def _step_emotional_assessment(self, message: ConversationMessage, conversation_history: List[ConversationMessage]) -> ECoTStep:
        """Step 3: Assess the user's overall emotional state and trends."""
        # Analyze recent conversation trend
        recent_messages = conversation_history[-10:] if len(conversation_history) > 10 else conversation_history
        trend_analysis = await self.emotion_service.analyze_conversation_trend(recent_messages)
        
        reasoning = f"""
        Emotional State Assessment:
        Current Message Emotion: {message.emotion_profile.primary_emotion.value if message.emotion_profile else 'Unknown'}
        
        Recent Conversation Trends:
        - Average Sentiment: {trend_analysis.get('avg_sentiment', 0.0):.2f}
        - Sentiment Trend: {'Improving' if trend_analysis.get('sentiment_trend', 0) > 0.1 else 'Declining' if trend_analysis.get('sentiment_trend', 0) < -0.1 else 'Stable'}
        - Emotional Volatility: {trend_analysis.get('emotional_volatility', 0.0):.2f}
        
        Internal Assessment: The user's emotional state appears to be 
        {'stable and positive' if trend_analysis.get('avg_sentiment', 0) > 0.3 else 'concerning and may need support' if trend_analysis.get('avg_sentiment', 0) < -0.3 else 'neutral to mixed'}.
        {'High emotional volatility detected - user may be going through a difficult period.' if trend_analysis.get('emotional_volatility', 0) > 0.5 else ''}
        """
        
        return ECoTStep(
            step_type='emotional_assessment',
            content=reasoning.strip(),
            confidence=0.7,
            timestamp=datetime.now()
        )

    async def _step_response_strategy(self, message: ConversationMessage, emotion_step: ECoTStep, 
                                    context_step: ECoTStep, assessment_step: ECoTStep) -> ECoTStep:
        """Step 4: Plan the response strategy based on emotional analysis and context."""
        
        # Determine response approach based on emotional state
        if message.emotion_profile and message.emotion_profile.is_crisis_signal:
            strategy = "CRISIS_INTERVENTION"
            approach = "Immediate supportive response with crisis resources and professional help guidance."
        elif message.emotion_profile and message.emotion_profile.sentiment_polarity < -0.5:
            strategy = "EMOTIONAL_SUPPORT"
            approach = "Empathetic validation with gentle encouragement and coping suggestions."
        elif message.emotion_profile and message.emotion_profile.sentiment_polarity > 0.5:
            strategy = "POSITIVE_REINFORCEMENT"
            approach = "Celebrate positive emotions while maintaining authentic connection."
        else:
            strategy = "BALANCED_ENGAGEMENT"
            approach = "Neutral, supportive engagement with focus on understanding and connection."
        
        reasoning = f"""
        Response Strategy Planning:
        Selected Strategy: {strategy}
        Approach: {approach}
        
        Key Considerations:
        - User's current emotional state requires {'immediate crisis intervention' if strategy == 'CRISIS_INTERVENTION' else 'supportive empathy' if strategy == 'EMOTIONAL_SUPPORT' else 'positive engagement' if strategy == 'POSITIVE_REINFORCEMENT' else 'balanced understanding'}
        - Available context {'provides rich personalization opportunities' if 'Rich contextual' in context_step.content else 'is limited, focusing on present moment'}
        - Response should be {'directive and resource-focused' if strategy == 'CRISIS_INTERVENTION' else 'validating and supportive' if strategy == 'EMOTIONAL_SUPPORT' else 'celebratory and connecting' if strategy == 'POSITIVE_REINFORCEMENT' else 'understanding and curious'}
        
        Internal Decision: I will craft a response that prioritizes {strategy.lower().replace('_', ' ')} while maintaining authenticity and avoiding clinical language.
        """
        
        return ECoTStep(
            step_type='response_planning',
            content=reasoning.strip(),
            confidence=0.9,
            timestamp=datetime.now()
        )

    async def _step_response_generation(self, message: ConversationMessage, previous_steps: List[ECoTStep]) -> ECoTStep:
        """Step 5: Generate the actual response using LLM or fallback templates."""

        # Try LLM generation first, fall back to templates if needed
        if self.llm_client:
            response = await self._generate_llm_response(message, previous_steps)
        else:
            response = self._generate_template_response(message)

        return ECoTStep(
            step_type='response_generation',
            content=response,
            confidence=0.9 if self.llm_client else 0.7,
            timestamp=datetime.now()
        )

    async def _generate_llm_response(self, message: ConversationMessage, previous_steps: List[ECoTStep]) -> str:
        """Generate response using Gemini LLM with human persona prompt."""

        # Build context from previous steps
        context_summary = "\n".join([
            f"Step {i+1} ({step.step_type}): {step.content[:200]}..."
            for i, step in enumerate(previous_steps)
        ])

        # Create the prompt for Gemini
        prompt = f"""
        {self._get_aura_system_prompt()}

        **Context from your internal reflection:**
        {context_summary}

        **User's message:** {message.message}

        **Crisis detected:** {message.emotion_profile.is_crisis_signal if message.emotion_profile else False}

        Respond as Aura, the caring friend. Show your internal reflection in <reflection> tags, then provide your response.
        """

        try:
            response = self.llm_client.generate_content(prompt)

            # Extract just the response part (after reflection)
            response_text = response.text.strip()

            # If response contains reflection tags, extract only the final response
            if "</reflection>" in response_text:
                response_text = response_text.split("</reflection>")[-1].strip()

            return response_text

        except Exception as e:
            print(f"LLM generation failed: {e}")
            return self._generate_template_response(message)

    def _generate_template_response(self, message: ConversationMessage) -> str:
        """Generate response using human templates as fallback."""

        if not message.emotion_profile:
            return "I'm here and I'm listening. How are you feeling right now?"

        # Handle crisis situations first
        if message.emotion_profile.is_crisis_signal:
            return human_templates.get_crisis_response("critical")

        # Use contextual response based on emotion
        return human_templates.get_contextual_response(
            emotion_type=message.emotion_profile.primary_emotion.value,
            intensity=message.emotion_profile.emotion_intensity,
            sentiment=message.emotion_profile.sentiment_polarity
        )

    async def _step_response_validation(self, response: str, original_message: ConversationMessage) -> ECoTStep:
        """Step 6: Validate the response for appropriateness and safety."""
        
        # Check for potential issues
        validation_checks = {
            'appropriate_length': 50 <= len(response) <= 500,
            'no_clinical_language': not any(word in response.lower() for word in ['diagnose', 'disorder', 'therapy', 'treatment']),
            'empathetic_tone': any(word in response.lower() for word in ['understand', 'hear', 'feel', 'sounds', 'seems']),
            'crisis_appropriate': not original_message.emotion_profile.is_crisis_signal or 'help' in response.lower()
        }
        
        validation_score = sum(validation_checks.values()) / len(validation_checks)
        
        reasoning = f"""
        Response Validation:
        Validation Checks:
        - Appropriate Length: {'✓' if validation_checks['appropriate_length'] else '✗'}
        - Avoids Clinical Language: {'✓' if validation_checks['no_clinical_language'] else '✗'}
        - Empathetic Tone: {'✓' if validation_checks['empathetic_tone'] else '✗'}
        - Crisis Appropriate: {'✓' if validation_checks['crisis_appropriate'] else '✗'}
        
        Overall Validation Score: {validation_score:.2f}
        
        Internal Assessment: Response {'meets quality standards' if validation_score >= 0.75 else 'needs improvement'}.
        """
        
        return ECoTStep(
            step_type='response_validation',
            content=reasoning.strip(),
            confidence=validation_score,
            timestamp=datetime.now()
        )

    def _generate_crisis_response(self, message: ConversationMessage) -> str:
        """Generate a crisis intervention response."""
        return ("I'm really worried about you right now. What you're feeling is so valid, and I want you to know that you matter. "
                "I think it would help to talk to someone who can give you the support you deserve - maybe calling 988 or texting HOME to 741741? "
                "You don't have to carry this alone. There are people who care and want to help.")

    def _generate_supportive_response(self, message: ConversationMessage) -> str:
        """Generate a supportive response for negative emotions."""
        return ("That sounds really hard. I can hear how much you're struggling right now, and that makes complete sense given what you're dealing with. "
                "Sometimes it helps just to have someone listen. I'm here if you want to tell me more about what's weighing on you.")

    def _generate_positive_response(self, message: ConversationMessage) -> str:
        """Generate a response that reinforces positive emotions."""
        return ("I love hearing this! It sounds like something really good is happening for you. "
                "Those bright moments mean so much. What's making you feel this way?")

    def _generate_balanced_response(self, message: ConversationMessage) -> str:
        """Generate a balanced, curious response."""
        return ("I'm glad you shared that with me. I'm here and I'm listening. "
                "How are things feeling for you lately?")
