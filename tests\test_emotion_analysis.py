"""
Unit tests for the Emotion Analysis Service.
Tests emotion detection, sentiment analysis, and crisis signal detection.
"""
import pytest
import asyncio
from datetime import datetime

from src.ai_companion.emotion.emotion_analysis_service import EmotionAnalysisService
from src.ai_companion.models.data_models import EmotionType, ConversationMessage


class TestEmotionAnalysisService:
    """Test suite for EmotionAnalysisService."""
    
    @pytest.fixture
    def emotion_service(self):
        """Create an EmotionAnalysisService instance for testing."""
        return EmotionAnalysisService()
    
    @pytest.mark.asyncio
    async def test_positive_emotion_detection(self, emotion_service):
        """Test detection of positive emotions."""
        message = "I'm so happy and excited about this amazing opportunity!"
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        assert emotion_profile.primary_emotion == EmotionType.JOY
        assert emotion_profile.sentiment_polarity > 0.5
        assert emotion_profile.emotion_intensity > 0.6
        assert not emotion_profile.is_crisis_signal
        assert emotion_profile.confidence_score > 0.5
    
    @pytest.mark.asyncio
    async def test_negative_emotion_detection(self, emotion_service):
        """Test detection of negative emotions."""
        message = "I'm feeling really sad and depressed about everything."
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        assert emotion_profile.primary_emotion == EmotionType.SADNESS
        assert emotion_profile.sentiment_polarity < -0.3
        assert emotion_profile.emotion_intensity > 0.5
        assert not emotion_profile.is_crisis_signal  # Sad but not crisis
        assert emotion_profile.confidence_score > 0.5
    
    @pytest.mark.asyncio
    async def test_crisis_signal_detection(self, emotion_service):
        """Test detection of crisis signals."""
        crisis_messages = [
            "I want to kill myself",
            "I don't want to live anymore",
            "Everyone would be better off without me",
            "I'm thinking about suicide"
        ]
        
        for message in crisis_messages:
            emotion_profile = await emotion_service.analyze_emotion(message)
            assert emotion_profile.is_crisis_signal, f"Failed to detect crisis in: {message}"
    
    @pytest.mark.asyncio
    async def test_neutral_emotion_detection(self, emotion_service):
        """Test detection of neutral emotions."""
        message = "I went to the store and bought some groceries."
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        # Should be neutral or very low intensity
        assert abs(emotion_profile.sentiment_polarity) < 0.3
        assert emotion_profile.emotion_intensity < 0.6
        assert not emotion_profile.is_crisis_signal
    
    @pytest.mark.asyncio
    async def test_anger_detection(self, emotion_service):
        """Test detection of anger emotions."""
        message = "I'm so angry and frustrated with this situation!"
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        assert emotion_profile.primary_emotion == EmotionType.ANGER
        assert emotion_profile.arousal_level > 0.6  # Anger should have high arousal
        assert emotion_profile.emotion_intensity > 0.5
    
    @pytest.mark.asyncio
    async def test_fear_detection(self, emotion_service):
        """Test detection of fear emotions."""
        message = "I'm terrified and scared about what might happen."
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        assert emotion_profile.primary_emotion == EmotionType.FEAR
        assert emotion_profile.sentiment_polarity < 0
        assert emotion_profile.arousal_level > 0.5
    
    @pytest.mark.asyncio
    async def test_conversation_trend_analysis(self, emotion_service):
        """Test analysis of emotional trends across conversation."""
        # Create sample conversation messages
        messages = []
        
        # Simulate declining mood over time
        message_texts = [
            "Having a great day!",
            "Things are going okay.",
            "Feeling a bit down today.",
            "Really struggling with everything.",
            "Don't know how much more I can take."
        ]
        
        for i, text in enumerate(message_texts):
            emotion_profile = await emotion_service.analyze_emotion(text)
            message = ConversationMessage(
                id=f"msg_{i}",
                timestamp=datetime.now(),
                sender='user',
                message=text,
                emotion_profile=emotion_profile
            )
            messages.append(message)
        
        # Analyze trend
        trend_analysis = await emotion_service.analyze_conversation_trend(messages)
        
        assert 'avg_sentiment' in trend_analysis
        assert 'sentiment_trend' in trend_analysis
        assert 'emotional_volatility' in trend_analysis
        
        # Should detect declining sentiment
        assert trend_analysis['sentiment_trend'] < -0.1  # Negative trend
        assert trend_analysis['avg_sentiment'] < 0.2  # Overall negative
    
    @pytest.mark.asyncio
    async def test_empty_message_handling(self, emotion_service):
        """Test handling of empty or very short messages."""
        empty_messages = ["", "   ", "ok", "yes"]
        
        for message in empty_messages:
            emotion_profile = await emotion_service.analyze_emotion(message)
            
            # Should handle gracefully without errors
            assert emotion_profile is not None
            assert emotion_profile.confidence_score < 0.5  # Low confidence for short messages
    
    @pytest.mark.asyncio
    async def test_mixed_emotion_message(self, emotion_service):
        """Test handling of messages with mixed emotions."""
        message = "I'm happy about the promotion but sad to leave my current team."
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        # Should detect mixed emotions with moderate confidence
        assert emotion_profile is not None
        assert abs(emotion_profile.sentiment_polarity) < 0.8  # Not extremely positive or negative
        assert emotion_profile.confidence_score > 0.3
    
    @pytest.mark.asyncio
    async def test_arousal_level_calculation(self, emotion_service):
        """Test arousal level calculation for different emotions."""
        test_cases = [
            ("I'm so excited and thrilled!", 0.7),  # High arousal
            ("I'm feeling calm and peaceful.", 0.3),  # Low arousal
            ("I'm furious and enraged!", 0.8),  # Very high arousal
            ("I'm content and relaxed.", 0.2)  # Very low arousal
        ]
        
        for message, expected_min_arousal in test_cases:
            emotion_profile = await emotion_service.analyze_emotion(message)
            
            if expected_min_arousal > 0.6:
                assert emotion_profile.arousal_level > expected_min_arousal, f"Low arousal for: {message}"
            else:
                assert emotion_profile.arousal_level < 0.6, f"High arousal for: {message}"
    
    @pytest.mark.asyncio
    async def test_keyword_extraction(self, emotion_service):
        """Test extraction of emotional keywords."""
        message = "I'm feeling happy and excited but also a bit nervous."
        
        emotion_profile = await emotion_service.analyze_emotion(message)
        
        # Should extract emotional keywords
        assert len(emotion_profile.detected_keywords) > 0
        
        # Should contain some of the emotional words
        emotional_words = {'happy', 'excited', 'nervous'}
        detected_words = set(emotion_profile.detected_keywords)
        assert len(emotional_words.intersection(detected_words)) > 0
    
    def test_emotion_lexicon_loading(self, emotion_service):
        """Test that emotion lexicon is properly loaded."""
        assert emotion_service.emotion_lexicon is not None
        assert 'joy' in emotion_service.emotion_lexicon
        assert 'sadness' in emotion_service.emotion_lexicon
        assert 'anger' in emotion_service.emotion_lexicon
        assert 'fear' in emotion_service.emotion_lexicon
        
        # Check that lexicon contains expected words
        assert 'happy' in emotion_service.emotion_lexicon['joy']
        assert 'sad' in emotion_service.emotion_lexicon['sadness']
    
    def test_crisis_patterns_loading(self, emotion_service):
        """Test that crisis detection patterns are properly loaded."""
        assert emotion_service.crisis_patterns is not None
        assert len(emotion_service.crisis_patterns) > 0
        
        # Test that patterns can detect crisis language
        crisis_text = "I want to kill myself"
        is_crisis = any(pattern.search(crisis_text) for pattern in emotion_service.crisis_patterns)
        assert is_crisis


# Integration test
@pytest.mark.asyncio
async def test_emotion_service_integration():
    """Integration test for the complete emotion analysis workflow."""
    service = EmotionAnalysisService()
    
    # Test a complete conversation flow
    conversation_messages = [
        "Good morning! Starting the day with a positive attitude.",
        "Work meeting went well, feeling productive.",
        "Lunch with friends was really nice.",
        "Afternoon got a bit stressful with deadlines.",
        "Managed to finish everything, feeling relieved.",
        "Evening workout helped me unwind.",
        "Good night, feeling grateful for today."
    ]
    
    analyzed_messages = []
    
    for i, text in enumerate(conversation_messages):
        emotion_profile = await service.analyze_emotion(text)
        
        message = ConversationMessage(
            id=f"integration_msg_{i}",
            timestamp=datetime.now(),
            sender='user',
            message=text,
            emotion_profile=emotion_profile
        )
        analyzed_messages.append(message)
        
        # Verify each analysis
        assert emotion_profile is not None
        assert emotion_profile.confidence_score > 0.1
        assert not emotion_profile.is_crisis_signal  # None should be crisis
    
    # Analyze overall trend
    trend_analysis = await service.analyze_conversation_trend(analyzed_messages)
    
    assert trend_analysis['avg_sentiment'] > 0  # Overall positive conversation
    assert 'emotional_volatility' in trend_analysis
    assert trend_analysis['emotional_volatility'] < 1.0  # Reasonable volatility


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
