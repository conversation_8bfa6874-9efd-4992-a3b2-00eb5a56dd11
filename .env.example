# Aura AI Companion - Environment Variables Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# Google Gemini API Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Optional: Other LLM providers
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database Configuration (for production)
DATABASE_URL=postgresql://user:password@localhost:5432/aura_db

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Application Settings
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Security Settings
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret_here

# Privacy Settings
DIFFERENTIAL_PRIVACY_EPSILON=1.0
DATA_RETENTION_DAYS=365

# Crisis Intervention Settings
CRISIS_ALERT_WEBHOOK=your_crisis_alert_webhook_url
ENABLE_CRISIS_LOGGING=true

# Monitoring (optional)
SENTRY_DSN=your_sentry_dsn_here
PROMETHEUS_PORT=9090
