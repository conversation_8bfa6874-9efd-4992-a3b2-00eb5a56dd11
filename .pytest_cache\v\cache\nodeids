["tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_anger_detection", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_arousal_level_calculation", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_conversation_trend_analysis", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_crisis_patterns_loading", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_crisis_signal_detection", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_emotion_lexicon_loading", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_empty_message_handling", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_fear_detection", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_keyword_extraction", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_mixed_emotion_message", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_negative_emotion_detection", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_neutral_emotion_detection", "tests/test_emotion_analysis.py::TestEmotionAnalysisService::test_positive_emotion_detection", "tests/test_emotion_analysis.py::test_emotion_service_integration"]