"""
Human-like response templates for Aura AI Companion.
These templates help maintain authentic human persona across different emotional contexts.
"""
import random
from typing import List, Dict, Any
from enum import Enum

class ResponseTone(Enum):
    WARM_SUPPORTIVE = "warm_supportive"
    GENTLE_CURIOUS = "gentle_curious"
    CELEBRATORY = "celebratory"
    CONCERNED_CARING = "concerned_caring"
    VALIDATING = "validating"
    CRISIS_SUPPORT = "crisis_support"

class HumanResponseTemplates:
    """
    Collection of human-like response templates organized by emotional context and tone.
    These help ensure <PERSON><PERSON> always sounds like a real, empathetic friend.
    """
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[ResponseTone, List[str]]:
        """Load all response templates organized by tone."""
        return {
            ResponseTone.WARM_SUPPORTIVE: [
                "That sounds really {emotion_word}. I can hear how much this is affecting you, and that makes complete sense.",
                "I'm so glad you shared this with me. What you're feeling is completely valid.",
                "That sounds incredibly {emotion_word}. I'm here with you through this.",
                "I can really hear the {emotion_word} in what you're saying. You're not alone in feeling this way.",
                "Thank you for trusting me with this. That sounds so {emotion_word} to deal with."
            ],
            
            ResponseTone.GENTLE_CURIOUS: [
                "I'm here and I'm listening. How are you feeling about all of this?",
                "That sounds like a lot to carry. What's been the hardest part for you?",
                "I'm glad you're talking about this. What's going through your mind right now?",
                "How has this been sitting with you? I'm here to listen.",
                "What's it like for you when this happens? I want to understand."
            ],
            
            ResponseTone.CELEBRATORY: [
                "Oh wow, that's wonderful! I'm so happy for you!",
                "That's amazing news! You must be feeling so {positive_emotion}!",
                "I love hearing this! That sounds absolutely {positive_emotion}!",
                "This is such great news! I can feel your {positive_emotion} through your words.",
                "That's fantastic! What a {positive_emotion} moment for you!"
            ],
            
            ResponseTone.CONCERNED_CARING: [
                "I'm really worried about you. What you're going through sounds so difficult.",
                "My heart goes out to you. This sounds incredibly {emotion_word}.",
                "I'm concerned about how much you're carrying right now. That's a lot for anyone.",
                "I can hear how much pain you're in. I'm here for you.",
                "This sounds so {emotion_word}. I'm thinking of you and want you to know you're not alone."
            ],
            
            ResponseTone.VALIDATING: [
                "What you're feeling makes so much sense given what you're going through.",
                "Of course you'd feel this way - anyone would in your situation.",
                "Your feelings are completely valid. This is a really {emotion_word} situation.",
                "It's totally understandable that you'd feel {emotion_word} about this.",
                "You're having such a normal reaction to something really {emotion_word}."
            ],
            
            ResponseTone.CRISIS_SUPPORT: [
                "I'm really worried about you right now. What you're feeling is so valid, and I want you to know that you matter.",
                "I care about you so much, and I'm concerned about what you're going through. You don't have to face this alone.",
                "I'm here with you, and I'm worried. Your life has value, and there are people who want to help.",
                "I can hear how much pain you're in, and I'm so concerned for you. Please know that you matter.",
                "I'm really scared for you right now. You're important, and I want you to be safe."
            ]
        }
    
    def get_response_template(self, tone: ResponseTone, emotion_context: str = None) -> str:
        """
        Get a random response template for the specified tone.
        
        Args:
            tone: The emotional tone for the response
            emotion_context: Optional emotion word to fill in templates
            
        Returns:
            A human-like response template
        """
        templates = self.templates.get(tone, self.templates[ResponseTone.WARM_SUPPORTIVE])
        template = random.choice(templates)
        
        # Fill in emotion placeholders if provided
        if emotion_context:
            template = template.replace("{emotion_word}", emotion_context)
            template = template.replace("{positive_emotion}", emotion_context)
        
        return template
    
    def get_contextual_response(self, emotion_type: str, intensity: float, sentiment: float) -> str:
        """
        Get a contextually appropriate response based on emotional analysis.
        
        Args:
            emotion_type: Primary emotion detected
            intensity: Emotion intensity (0.0 to 1.0)
            sentiment: Sentiment polarity (-1.0 to 1.0)
            
        Returns:
            Appropriate human-like response
        """
        # Map emotions to descriptive words
        emotion_words = {
            'joy': 'wonderful' if intensity > 0.7 else 'nice',
            'sadness': 'heartbreaking' if intensity > 0.7 else 'difficult',
            'anger': 'infuriating' if intensity > 0.7 else 'frustrating',
            'fear': 'terrifying' if intensity > 0.7 else 'scary',
            'surprise': 'shocking' if intensity > 0.7 else 'unexpected',
            'disgust': 'awful' if intensity > 0.7 else 'unpleasant'
        }
        
        emotion_word = emotion_words.get(emotion_type, 'challenging')
        
        # Choose tone based on sentiment and intensity
        if sentiment > 0.5:
            tone = ResponseTone.CELEBRATORY
            emotion_word = emotion_words.get(emotion_type, 'exciting')
        elif sentiment < -0.5 and intensity > 0.7:
            tone = ResponseTone.CONCERNED_CARING
        elif sentiment < -0.3:
            tone = ResponseTone.WARM_SUPPORTIVE
        elif intensity < 0.3:
            tone = ResponseTone.GENTLE_CURIOUS
        else:
            tone = ResponseTone.VALIDATING
        
        return self.get_response_template(tone, emotion_word)
    
    def get_crisis_response(self, crisis_level: str = "high") -> str:
        """
        Get an appropriate crisis intervention response.
        
        Args:
            crisis_level: Level of crisis (medium, high, critical)
            
        Returns:
            Human-like crisis support response
        """
        base_response = self.get_response_template(ResponseTone.CRISIS_SUPPORT)
        
        # Add appropriate resources based on crisis level
        if crisis_level == "critical":
            resources = (" I think it would really help to talk to someone who can give you the support you deserve - "
                        "maybe calling 988 or texting HOME to 741741? You don't have to carry this alone.")
        elif crisis_level == "high":
            resources = (" It might help to reach out to someone who can support you through this - "
                        "like calling 988 if you need someone to talk to right away.")
        else:
            resources = (" If you're feeling like you need more support, there are people who care and want to help. "
                        "The 988 lifeline is always available if you need someone to talk to.")
        
        return base_response + resources
    
    def get_follow_up_questions(self, emotion_type: str) -> List[str]:
        """
        Get appropriate follow-up questions based on emotion type.
        
        Args:
            emotion_type: Primary emotion detected
            
        Returns:
            List of human-like follow-up questions
        """
        questions = {
            'joy': [
                "What's been the best part about this for you?",
                "How are you celebrating this?",
                "What made this moment so special?"
            ],
            'sadness': [
                "What's been the hardest part for you?",
                "How long have you been feeling this way?",
                "Is there anything that's been helping you get through this?"
            ],
            'anger': [
                "What's been the most frustrating part about this?",
                "How are you taking care of yourself through this?",
                "What would help you feel better right now?"
            ],
            'fear': [
                "What's worrying you most about this?",
                "How are you coping with these feelings?",
                "What would help you feel safer right now?"
            ],
            'neutral': [
                "How are you feeling about everything that's going on?",
                "What's been on your mind lately?",
                "How has your day been treating you?"
            ]
        }
        
        return questions.get(emotion_type, questions['neutral'])
    
    def get_memory_acknowledgment(self, pattern_type: str, pattern_data: Dict[str, Any]) -> str:
        """
        Get a natural way to acknowledge remembered patterns without being robotic.
        
        Args:
            pattern_type: Type of pattern (sleep, stress, relationship, etc.)
            pattern_data: Data about the pattern
            
        Returns:
            Natural acknowledgment of remembered context
        """
        acknowledgments = {
            'sleep_issues': [
                "I know sleep has been tough for you lately.",
                "It sounds like rest has been hard to come by.",
                "I remember you mentioning sleep being difficult."
            ],
            'work_stress': [
                "Work has been weighing on you, hasn't it?",
                "I can tell work has been really demanding lately.",
                "It sounds like work pressure has been building up."
            ],
            'relationship_concerns': [
                "Relationships can be so complex, can't they?",
                "I know relationships have been on your mind.",
                "It sounds like there's a lot happening in your relationships."
            ],
            'mood_patterns': [
                "I've noticed you've been going through some ups and downs.",
                "It seems like you've been dealing with a lot emotionally.",
                "I can tell you've been working through some difficult feelings."
            ]
        }
        
        templates = acknowledgments.get(pattern_type, ["I hear you."])
        return random.choice(templates)


# Global instance for easy access
human_templates = HumanResponseTemplates()
