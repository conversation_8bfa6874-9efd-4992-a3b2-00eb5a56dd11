# Core dependencies for Aura AI Companion
numpy>=1.21.0
asyncio-mqtt>=0.11.0
python-dateutil>=2.8.0
typing-extensions>=4.0.0

# LLM Integration
google-generativeai>=0.3.0
python-dotenv>=1.0.0

# Data processing and analysis
pandas>=1.3.0
scikit-learn>=1.0.0

# Natural language processing
nltk>=3.7
spacy>=3.4.0

# Graph database and vector operations
networkx>=2.6.0
faiss-cpu>=1.7.0

# Privacy and cryptography
cryptography>=3.4.0
hashlib-compat>=1.0.0

# Web framework (for API endpoints)
fastapi>=0.70.0
uvicorn>=0.15.0
pydantic>=1.8.0

# Testing
pytest>=6.2.0
pytest-asyncio>=0.18.0
pytest-cov>=3.0.0

# Development tools
black>=22.0.0
flake8>=4.0.0
mypy>=0.910
pre-commit>=2.15.0

# Documentation
sphinx>=4.0.0
sphinx-rtd-theme>=1.0.0

# Logging and monitoring
structlog>=21.1.0
prometheus-client>=0.12.0

# Configuration management
pyyaml>=6.0
python-dotenv>=0.19.0

# Database (for production deployment)
sqlalchemy>=1.4.0
alembic>=1.7.0
psycopg2-binary>=2.9.0

# Redis (for caching and session management)
redis>=4.0.0
aioredis>=2.0.0

# HTTP client
httpx>=0.24.0
aiohttp>=3.8.0

# JSON handling
orjson>=3.6.0

# Time zone handling
pytz>=2021.3

# Validation and serialization
marshmallow>=3.14.0
cerberus>=1.3.0

# Machine learning (optional, for advanced features)
torch>=1.10.0
transformers>=4.15.0
sentence-transformers>=2.1.0

# Graph visualization (for development/debugging)
matplotlib>=3.5.0
plotly>=5.5.0

# Memory profiling (development)
memory-profiler>=0.60.0
psutil>=5.8.0

# Security
bcrypt>=3.2.0
passlib>=1.7.0
python-jose>=3.3.0

# Rate limiting
slowapi>=0.1.0
limits>=2.3.0

# Background tasks
celery>=5.2.0
redis>=4.0.0

# Monitoring and observability
opentelemetry-api>=1.12.0
opentelemetry-sdk>=1.12.0
opentelemetry-instrumentation-fastapi>=0.33b0

# Email (for notifications)
aiosmtplib>=1.1.0
jinja2>=3.0.0

# File handling
aiofiles>=0.8.0
python-multipart>=0.0.5

# UUID generation
uuid>=1.30

# Regular expressions (enhanced)
regex>=2022.1.18

# Concurrent processing
concurrent-futures>=3.1.0
asyncio-pool>=0.6.0

# Data validation
pydantic[email]>=1.8.0
validators>=0.18.0

# Caching
cachetools>=4.2.0
diskcache>=5.4.0

# Configuration
hydra-core>=1.1.0
omegaconf>=2.1.0

# Profiling and performance
line-profiler>=3.5.0
py-spy>=0.3.0

# Development utilities
ipython>=7.30.0
jupyter>=1.0.0
notebook>=6.4.0

# API documentation
fastapi-utils>=0.2.0
python-multipart>=0.0.5

# Health checks
healthcheck>=1.3.0

# Metrics collection
statsd>=3.3.0
datadog>=0.44.0

# Error tracking
sentry-sdk>=1.5.0

# Feature flags
flagsmith>=2.0.0

# A/B testing
split-io>=8.0.0
