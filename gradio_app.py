#!/usr/bin/env python3
"""
Gradio Web Interface for Aura AI Companion
Provides an interactive chat interface with real-time emotion analysis and insights.
"""
import gradio as gr
import asyncio
import json
import sys
import os
from datetime import datetime
from typing import List, Tuple, Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from ai_companion import create_aura_companion

# Global variables
aura_companion = None
current_user_id = "gradio_user"

def initialize_aura():
    """Initialize the Aura AI Companion."""
    global aura_companion
    if aura_companion is None:
        aura_companion = create_aura_companion()
        # Create user profile
        asyncio.run(aura_companion.create_user(
            user_id=current_user_id,
            preferences={
                "response_style": "warm_friend",
                "empathy_level": "high",
                "crisis_intervention": True
            }
        ))
    return aura_companion

def format_emotion_analysis(emotion_data: Dict[str, Any]) -> str:
    """Format emotion analysis data for display."""
    if not emotion_data:
        return "No emotion data available"
    
    emotion_emoji = {
        "joy": "😊", "sadness": "😢", "anger": "😠", 
        "fear": "😨", "surprise": "😲", "disgust": "🤢", "neutral": "😐"
    }
    
    primary_emotion = emotion_data.get('primary_emotion', 'neutral')
    intensity = emotion_data.get('emotion_intensity', 0.0)
    sentiment = emotion_data.get('sentiment_polarity', 0.0)
    crisis = emotion_data.get('is_crisis_signal', False)
    
    emoji = emotion_emoji.get(primary_emotion, "😐")
    
    analysis = f"""
**Emotion Analysis:**
{emoji} **Primary Emotion:** {primary_emotion.title()} (Intensity: {intensity:.2f})
📊 **Sentiment:** {sentiment:.2f} ({'Positive' if sentiment > 0 else 'Negative' if sentiment < 0 else 'Neutral'})
🚨 **Crisis Signal:** {'⚠️ YES' if crisis else '✅ No'}
"""
    return analysis

def format_risk_level(risk_level: str) -> str:
    """Format risk level with appropriate styling."""
    risk_colors = {
        "none": "🟢", "low": "🟡", "medium": "🟠", "high": "🔴", "critical": "🚨"
    }
    emoji = risk_colors.get(risk_level, "⚪")
    return f"{emoji} **Risk Level:** {risk_level.upper()}"

async def chat_with_aura(message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str, str]:
    """
    Process user message and return updated chat history and analysis.
    
    Args:
        message: User's input message
        history: Chat history as list of (user_msg, bot_msg) tuples
        
    Returns:
        Tuple of (updated_history, emotion_analysis, risk_info)
    """
    if not message.strip():
        return history, "", ""
    
    try:
        # Initialize Aura if needed
        aura = initialize_aura()
        
        # Process the message
        response = await aura.chat(current_user_id, message)
        
        # Update chat history
        history.append((message, response['response']))
        
        # Format analysis data
        emotion_analysis = format_emotion_analysis(response.get('emotion_analysis', {}))
        risk_info = format_risk_level(response.get('risk_level', 'none'))
        
        return history, emotion_analysis, risk_info
        
    except Exception as e:
        error_msg = f"Sorry, I encountered an error: {str(e)}"
        history.append((message, error_msg))
        return history, "Error in emotion analysis", "🔴 **Risk Level:** ERROR"

def sync_chat_with_aura(message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str, str]:
    """Synchronous wrapper for the async chat function."""
    return asyncio.run(chat_with_aura(message, history))

async def get_user_insights() -> str:
    """Get behavioral insights for the current user."""
    try:
        aura = initialize_aura()
        insights = await aura.get_user_insights(current_user_id)
        
        if not insights or not insights.get('recent_insights'):
            return "No behavioral patterns detected yet. Continue chatting to build insights!"
        
        insights_text = "**🧠 Behavioral Insights:**\n\n"
        for insight in insights['recent_insights'][:5]:  # Show top 5 insights
            insight_type = insight.get('insight_type', 'Unknown')
            confidence = insight.get('confidence_score', 0.0)
            data = insight.get('data', {})
            
            insights_text += f"• **{insight_type}** (Confidence: {confidence:.2f})\n"
            if insight_type == "PotentialSleepTime" and 'average_sleep_hour' in data:
                hour = data['average_sleep_hour']
                insights_text += f"  Sleep pattern: ~{hour:.1f}:00\n"
            elif insight_type == "PositiveCopingMechanism":
                mechanism = data.get('mechanism', 'Unknown')
                insights_text += f"  Coping strategy: {mechanism}\n"
            insights_text += "\n"
        
        return insights_text
        
    except Exception as e:
        return f"Error retrieving insights: {str(e)}"

def sync_get_user_insights() -> str:
    """Synchronous wrapper for getting insights."""
    return asyncio.run(get_user_insights())

async def export_user_data() -> str:
    """Export user data for privacy compliance."""
    try:
        aura = initialize_aura()
        exported_data = await aura.export_user_data(current_user_id)
        
        export_summary = f"""
**📁 Data Export Summary:**

**User Profile:** {'✅' if exported_data.get('user_profile') else '❌'}
**Conversation History:** {len(exported_data.get('conversation_history', []))} messages
**Insights:** {len(exported_data.get('insights_summary', []))} patterns detected
**Export Time:** {exported_data.get('export_timestamp', 'Unknown')}

*Note: In a production environment, this would generate a downloadable file.*
"""
        return export_summary
        
    except Exception as e:
        return f"Error exporting data: {str(e)}"

def sync_export_user_data() -> str:
    """Synchronous wrapper for data export."""
    return asyncio.run(export_user_data())

async def clear_user_data() -> str:
    """Clear all user data."""
    try:
        aura = initialize_aura()
        success = await aura.clear_user_data(current_user_id)
        
        if success:
            # Reinitialize user
            await aura.create_user(current_user_id)
            return "✅ **All user data has been cleared successfully.**\nA fresh user profile has been created."
        else:
            return "❌ **Failed to clear user data.**"
            
    except Exception as e:
        return f"❌ **Error clearing data:** {str(e)}"

def sync_clear_user_data() -> str:
    """Synchronous wrapper for clearing data."""
    return asyncio.run(clear_user_data())

# Custom CSS for better styling
custom_css = """
.gradio-container {
    max-width: 1200px !important;
}
.chat-message {
    padding: 10px;
    margin: 5px 0;
    border-radius: 10px;
}
.analysis-panel {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    border-left: 4px solid #007bff;
}
"""

def create_gradio_interface():
    """Create and configure the Gradio interface."""
    
    with gr.Blocks(css=custom_css, title="Aura AI Companion", theme=gr.themes.Soft()) as interface:
        
        # Header
        gr.Markdown("""
        # 🌟 Aura AI Companion
        
        **Your empathetic AI friend with deep emotional understanding**
        
        Chat with Aura and experience advanced emotion analysis, behavioral pattern recognition, 
        and privacy-preserving insights. Aura responds as a caring human friend, not an AI system.
        """)
        
        with gr.Row():
            # Left column - Chat interface
            with gr.Column(scale=2):
                gr.Markdown("## 💬 Chat with Aura")
                
                chatbot = gr.Chatbot(
                    label="Conversation",
                    height=400,
                    show_label=False,
                    avatar_images=("👤", "🌟")
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        placeholder="Type your message here... (Press Enter to send)",
                        label="Your Message",
                        lines=2,
                        scale=4
                    )
                    send_btn = gr.Button("Send", variant="primary", scale=1)
                
                clear_chat_btn = gr.Button("🗑️ Clear Chat", variant="secondary")
            
            # Right column - Analysis and controls
            with gr.Column(scale=1):
                gr.Markdown("## 📊 Real-time Analysis")
                
                emotion_display = gr.Markdown(
                    "Start chatting to see emotion analysis...",
                    label="Emotion Analysis"
                )
                
                risk_display = gr.Markdown(
                    "🟢 **Risk Level:** NONE",
                    label="Risk Assessment"
                )
                
                gr.Markdown("## 🧠 Insights & Privacy")
                
                insights_btn = gr.Button("📈 View Behavioral Insights", variant="secondary")
                insights_display = gr.Markdown("Click above to view insights...")
                
                export_btn = gr.Button("📁 Export My Data", variant="secondary")
                export_display = gr.Markdown("")
                
                clear_data_btn = gr.Button("🗑️ Clear All My Data", variant="stop")
                clear_display = gr.Markdown("")
        
        # Event handlers
        def handle_message(message, history):
            return sync_chat_with_aura(message, history)
        
        # Chat functionality
        msg_input.submit(
            handle_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, emotion_display, risk_display]
        ).then(
            lambda: "",  # Clear input
            outputs=[msg_input]
        )
        
        send_btn.click(
            handle_message,
            inputs=[msg_input, chatbot],
            outputs=[chatbot, emotion_display, risk_display]
        ).then(
            lambda: "",  # Clear input
            outputs=[msg_input]
        )
        
        clear_chat_btn.click(
            lambda: ([], "Start chatting to see emotion analysis...", "🟢 **Risk Level:** NONE"),
            outputs=[chatbot, emotion_display, risk_display]
        )
        
        # Insights and privacy controls
        insights_btn.click(
            sync_get_user_insights,
            outputs=[insights_display]
        )
        
        export_btn.click(
            sync_export_user_data,
            outputs=[export_display]
        )
        
        clear_data_btn.click(
            sync_clear_user_data,
            outputs=[clear_display]
        )
        
        # Footer
        gr.Markdown("""
        ---
        **🔒 Privacy Notice:** Your conversations are processed locally and can be exported or deleted at any time.
        **🚨 Crisis Support:** If you're in crisis, please contact emergency services or a crisis helpline immediately.
        """)
    
    return interface

if __name__ == "__main__":
    print("🌟 Starting Aura AI Companion Web Interface...")
    
    # Check for API key
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("⚠️  GOOGLE_API_KEY not found. Aura will use fallback responses.")
        print("To enable full LLM capabilities, set up your API key in .env file")
    else:
        print("✅ Google API key found - Full LLM integration enabled")
    
    # Create and launch the interface
    interface = create_gradio_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
