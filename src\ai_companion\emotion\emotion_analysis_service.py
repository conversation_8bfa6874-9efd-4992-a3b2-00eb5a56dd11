"""
Emotion Analysis Service for the Aura AI Companion.
Provides real-time emotional analysis of user messages using multiple techniques.
"""
import re
import asyncio
from typing import List, Dict, Optional
from datetime import datetime
import numpy as np

from ..models.data_models import EmotionProfile, EmotionType, ConversationMessage


class EmotionAnalysisService:
    """
    Analyzes user messages to extract emotional content and detect crisis signals.
    Uses a combination of lexicon-based analysis, pattern matching, and ML models.
    """
    
    def __init__(self):
        self.emotion_lexicon = self._load_emotion_lexicon()
        self.crisis_patterns = self._load_crisis_patterns()
        self.sentiment_model = None  # Would be loaded from a trained model
        print("EmotionAnalysisService initialized.")
    
    def _load_emotion_lexicon(self) -> Dict[str, Dict[str, float]]:
        """
        Loads emotion lexicon mapping words to emotion scores.
        In production, this would load from a comprehensive emotion database.
        """
        return {
            'joy': {
                'happy': 0.8, 'excited': 0.9, 'thrilled': 0.95, 'delighted': 0.85,
                'cheerful': 0.7, 'elated': 0.9, 'joyful': 0.9, 'pleased': 0.6
            },
            'sadness': {
                'sad': 0.8, 'depressed': 0.95, 'miserable': 0.9, 'heartbroken': 0.95,
                'gloomy': 0.7, 'melancholy': 0.75, 'sorrowful': 0.8, 'dejected': 0.85
            },
            'anger': {
                'angry': 0.8, 'furious': 0.95, 'irritated': 0.6, 'enraged': 0.9,
                'annoyed': 0.5, 'livid': 0.9, 'outraged': 0.85, 'frustrated': 0.7
            },
            'fear': {
                'scared': 0.8, 'terrified': 0.95, 'anxious': 0.7, 'worried': 0.6,
                'nervous': 0.6, 'frightened': 0.85, 'panicked': 0.9, 'afraid': 0.75
            },
            'surprise': {
                'surprised': 0.8, 'amazed': 0.85, 'astonished': 0.9, 'shocked': 0.8,
                'stunned': 0.85, 'bewildered': 0.7, 'startled': 0.75
            }
        }
    
    def _load_crisis_patterns(self) -> List[re.Pattern]:
        """
        Loads regex patterns for crisis detection.
        These patterns are carefully designed to catch potential self-harm indicators.
        """
        patterns = [
            re.compile(r'\b(want to die|kill myself|end it all|not worth living)\b', re.IGNORECASE),
            re.compile(r'\b(suicide|suicidal|self harm|hurt myself)\b', re.IGNORECASE),
            re.compile(r'\b(no point|give up|can\'t go on|hopeless)\b', re.IGNORECASE),
            re.compile(r'\b(everyone would be better|burden to everyone)\b', re.IGNORECASE)
        ]
        return patterns
    
    async def analyze_emotion(self, message: str) -> EmotionProfile:
        """
        Performs comprehensive emotional analysis of a message.
        
        Args:
            message: The user's message text
            
        Returns:
            EmotionProfile containing detailed emotional analysis
        """
        # Normalize the message
        normalized_message = message.lower().strip()
        words = re.findall(r'\b\w+\b', normalized_message)
        
        # Calculate emotion scores
        emotion_scores = self._calculate_emotion_scores(words)
        
        # Determine primary emotion
        primary_emotion = max(emotion_scores.items(), key=lambda x: x[1])
        primary_emotion_type = EmotionType(primary_emotion[0])
        emotion_intensity = primary_emotion[1]
        
        # Calculate sentiment polarity
        sentiment_polarity = self._calculate_sentiment_polarity(words)
        
        # Calculate arousal level
        arousal_level = self._calculate_arousal_level(words, emotion_scores)
        
        # Detect crisis signals
        is_crisis_signal = self._detect_crisis_signals(message)
        
        # Calculate confidence score
        confidence_score = self._calculate_confidence(emotion_scores, len(words))
        
        # Extract detected keywords
        detected_keywords = self._extract_emotional_keywords(words)
        
        return EmotionProfile(
            primary_emotion=primary_emotion_type,
            emotion_intensity=emotion_intensity,
            sentiment_polarity=sentiment_polarity,
            arousal_level=arousal_level,
            is_crisis_signal=is_crisis_signal,
            confidence_score=confidence_score,
            detected_keywords=detected_keywords
        )
    
    def _calculate_emotion_scores(self, words: List[str]) -> Dict[str, float]:
        """Calculate scores for each emotion category based on word presence."""
        emotion_scores = {emotion: 0.0 for emotion in self.emotion_lexicon.keys()}
        emotion_scores['neutral'] = 0.5  # Default neutral score
        
        for word in words:
            for emotion, lexicon in self.emotion_lexicon.items():
                if word in lexicon:
                    emotion_scores[emotion] += lexicon[word]
        
        # Normalize scores
        total_score = sum(emotion_scores.values())
        if total_score > 0:
            emotion_scores = {k: v / total_score for k, v in emotion_scores.items()}
        
        return emotion_scores
    
    def _calculate_sentiment_polarity(self, words: List[str]) -> float:
        """Calculate overall sentiment polarity from -1.0 to 1.0."""
        positive_words = {'good', 'great', 'awesome', 'wonderful', 'excellent', 'amazing', 'love', 'like'}
        negative_words = {'bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'worst', 'disgusting'}
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        
        total_sentiment_words = positive_count + negative_count
        if total_sentiment_words == 0:
            return 0.0
        
        return (positive_count - negative_count) / total_sentiment_words
    
    def _calculate_arousal_level(self, words: List[str], emotion_scores: Dict[str, float]) -> float:
        """Calculate arousal level based on emotion intensity and specific words."""
        high_arousal_words = {'excited', 'thrilled', 'furious', 'terrified', 'panicked', 'ecstatic'}
        low_arousal_words = {'calm', 'peaceful', 'relaxed', 'serene', 'tranquil', 'mellow'}
        
        high_arousal_count = sum(1 for word in words if word in high_arousal_words)
        low_arousal_count = sum(1 for word in words if word in low_arousal_words)
        
        # Base arousal from emotion intensity
        base_arousal = emotion_scores.get('anger', 0) * 0.8 + emotion_scores.get('fear', 0) * 0.7 + emotion_scores.get('joy', 0) * 0.6
        
        # Adjust based on specific arousal words
        arousal_adjustment = (high_arousal_count - low_arousal_count) * 0.1
        
        return max(0.0, min(1.0, base_arousal + arousal_adjustment))
    
    def _detect_crisis_signals(self, message: str) -> bool:
        """Detect potential crisis or self-harm indicators in the message."""
        for pattern in self.crisis_patterns:
            if pattern.search(message):
                return True
        return False
    
    def _calculate_confidence(self, emotion_scores: Dict[str, float], word_count: int) -> float:
        """Calculate confidence score based on emotion clarity and message length."""
        # Higher confidence for clearer emotional signals
        max_emotion_score = max(emotion_scores.values())
        emotion_clarity = max_emotion_score - 0.5  # How far from neutral
        
        # Adjust for message length (longer messages generally more reliable)
        length_factor = min(1.0, word_count / 20.0)
        
        confidence = (emotion_clarity * 0.7 + length_factor * 0.3)
        return max(0.1, min(1.0, confidence))
    
    def _extract_emotional_keywords(self, words: List[str]) -> List[str]:
        """Extract words that contributed to the emotional analysis."""
        emotional_keywords = []
        for word in words:
            for emotion_lexicon in self.emotion_lexicon.values():
                if word in emotion_lexicon:
                    emotional_keywords.append(word)
                    break
        return list(set(emotional_keywords))  # Remove duplicates
    
    async def analyze_conversation_trend(self, messages: List[ConversationMessage]) -> Dict[str, float]:
        """
        Analyze emotional trends across a conversation.
        
        Args:
            messages: List of conversation messages with emotion profiles
            
        Returns:
            Dictionary containing trend analysis metrics
        """
        if not messages:
            return {}
        
        # Extract emotion data from messages
        emotion_data = []
        for msg in messages:
            if msg.emotion_profile:
                emotion_data.append({
                    'timestamp': msg.timestamp,
                    'sentiment': msg.emotion_profile.sentiment_polarity,
                    'intensity': msg.emotion_profile.emotion_intensity,
                    'arousal': msg.emotion_profile.arousal_level
                })
        
        if not emotion_data:
            return {}
        
        # Calculate trends
        sentiments = [data['sentiment'] for data in emotion_data]
        intensities = [data['intensity'] for data in emotion_data]
        arousals = [data['arousal'] for data in emotion_data]
        
        return {
            'avg_sentiment': np.mean(sentiments),
            'sentiment_trend': self._calculate_trend(sentiments),
            'avg_intensity': np.mean(intensities),
            'intensity_trend': self._calculate_trend(intensities),
            'avg_arousal': np.mean(arousals),
            'arousal_trend': self._calculate_trend(arousals),
            'emotional_volatility': np.std(sentiments)
        }
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate the trend direction of a series of values."""
        if len(values) < 2:
            return 0.0
        
        # Simple linear trend calculation
        x = list(range(len(values)))
        correlation = np.corrcoef(x, values)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
