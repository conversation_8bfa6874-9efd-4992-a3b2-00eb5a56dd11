"""
Privacy and Data Protection utilities for the Aura AI Companion.
Implements differential privacy and other privacy-preserving techniques.
"""
import random
import hashlib
import numpy as np
from typing import Dict, List, Any, Union
from datetime import datetime, <PERSON><PERSON><PERSON>


def apply_differential_privacy(data: Dict[str, Any], epsilon: float = 1.0) -> Dict[str, Any]:
    """
    Apply differential privacy using the Laplace mechanism.
    
    Args:
        data: Dictionary containing numerical data to protect
        epsilon: Privacy parameter (smaller = more privacy, more noise)
        
    Returns:
        Dictionary with privacy-preserving noise added
    """
    private_data = {}
    
    for key, value in data.items():
        if isinstance(value, (int, float)):
            # Add Laplace noise calibrated to sensitivity and epsilon
            sensitivity = 1.0  # Assuming unit sensitivity for simplicity
            noise = np.random.laplace(0, sensitivity / epsilon)
            private_data[key] = value + noise
        elif isinstance(value, dict):
            # Recursively apply to nested dictionaries
            private_data[key] = apply_differential_privacy(value, epsilon)
        elif isinstance(value, list) and all(isinstance(x, (int, float)) for x in value):
            # Apply noise to numerical lists
            sensitivity = 1.0
            noise_list = [np.random.laplace(0, sensitivity / epsilon) for _ in value]
            private_data[key] = [v + n for v, n in zip(value, noise_list)]
        else:
            # Non-numerical data passed through unchanged
            private_data[key] = value
    
    return private_data


def anonymize_user_data(data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
    """
    Anonymize user data by removing or hashing identifying information.
    
    Args:
        data: User data dictionary
        user_id: Original user identifier
        
    Returns:
        Anonymized data dictionary
    """
    # Create consistent anonymous ID
    anonymous_id = hashlib.sha256(user_id.encode()).hexdigest()[:16]
    
    anonymized = {}
    
    for key, value in data.items():
        if key in ['user_id', 'id', 'identifier']:
            anonymized[key] = anonymous_id
        elif key in ['name', 'email', 'phone', 'address']:
            # Remove personally identifiable information
            anonymized[key] = '[REDACTED]'
        elif isinstance(value, str) and len(value) > 50:
            # Hash long text fields that might contain identifying info
            anonymized[key] = hashlib.sha256(value.encode()).hexdigest()[:32]
        elif isinstance(value, dict):
            anonymized[key] = anonymize_user_data(value, user_id)
        elif isinstance(value, list):
            anonymized[key] = [
                anonymize_user_data(item, user_id) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            anonymized[key] = value
    
    return anonymized


def k_anonymize_dataset(dataset: List[Dict[str, Any]], k: int = 5, 
                       quasi_identifiers: List[str] = None) -> List[Dict[str, Any]]:
    """
    Apply k-anonymity to a dataset by generalizing quasi-identifiers.
    
    Args:
        dataset: List of data records
        k: Minimum group size for anonymity
        quasi_identifiers: List of field names that are quasi-identifiers
        
    Returns:
        K-anonymized dataset
    """
    if not quasi_identifiers:
        quasi_identifiers = ['age', 'location', 'occupation', 'gender']
    
    # Group records by quasi-identifier combinations
    groups = {}
    for record in dataset:
        # Create key from quasi-identifiers
        key_parts = []
        for qi in quasi_identifiers:
            if qi in record:
                key_parts.append(str(record[qi]))
        key = '|'.join(key_parts)
        
        if key not in groups:
            groups[key] = []
        groups[key].append(record)
    
    # Generalize groups smaller than k
    anonymized_dataset = []
    for group_records in groups.values():
        if len(group_records) < k:
            # Generalize quasi-identifiers for small groups
            for record in group_records:
                anonymized_record = record.copy()
                for qi in quasi_identifiers:
                    if qi in anonymized_record:
                        anonymized_record[qi] = _generalize_value(anonymized_record[qi])
                anonymized_dataset.append(anonymized_record)
        else:
            # Group is large enough, keep as-is
            anonymized_dataset.extend(group_records)
    
    return anonymized_dataset


def _generalize_value(value: Any) -> str:
    """Generalize a value for k-anonymity."""
    if isinstance(value, int):
        # Generalize numbers to ranges
        if value < 25:
            return "18-25"
        elif value < 35:
            return "25-35"
        elif value < 50:
            return "35-50"
        else:
            return "50+"
    elif isinstance(value, str):
        # Generalize strings to categories
        return "GENERALIZED"
    else:
        return str(value)


class PrivacyAuditLogger:
    """
    Logger for privacy-related events and data access.
    Helps ensure compliance with privacy regulations.
    """
    
    def __init__(self):
        self.access_log = []
        self.processing_log = []
    
    def log_data_access(self, user_id: str, data_type: str, purpose: str, 
                       accessor: str = "system") -> None:
        """
        Log data access events.
        
        Args:
            user_id: User whose data was accessed
            data_type: Type of data accessed
            purpose: Purpose of the access
            accessor: Who/what accessed the data
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_id': hashlib.sha256(user_id.encode()).hexdigest()[:16],  # Anonymized
            'data_type': data_type,
            'purpose': purpose,
            'accessor': accessor,
            'event_type': 'data_access'
        }
        self.access_log.append(log_entry)
    
    def log_data_processing(self, user_id: str, processing_type: str, 
                          privacy_technique: str = None) -> None:
        """
        Log data processing events.
        
        Args:
            user_id: User whose data was processed
            processing_type: Type of processing performed
            privacy_technique: Privacy technique applied (if any)
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'user_id': hashlib.sha256(user_id.encode()).hexdigest()[:16],  # Anonymized
            'processing_type': processing_type,
            'privacy_technique': privacy_technique,
            'event_type': 'data_processing'
        }
        self.processing_log.append(log_entry)
    
    def get_user_privacy_report(self, user_id: str) -> Dict[str, Any]:
        """
        Generate a privacy report for a specific user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Privacy report dictionary
        """
        anonymized_user_id = hashlib.sha256(user_id.encode()).hexdigest()[:16]
        
        user_access_events = [
            event for event in self.access_log 
            if event['user_id'] == anonymized_user_id
        ]
        
        user_processing_events = [
            event for event in self.processing_log 
            if event['user_id'] == anonymized_user_id
        ]
        
        return {
            'user_id': anonymized_user_id,
            'report_generated': datetime.now().isoformat(),
            'total_access_events': len(user_access_events),
            'total_processing_events': len(user_processing_events),
            'access_events': user_access_events,
            'processing_events': user_processing_events,
            'privacy_techniques_used': list(set(
                event.get('privacy_technique') for event in user_processing_events
                if event.get('privacy_technique')
            ))
        }


class ConsentManager:
    """
    Manages user consent for different types of data processing.
    """
    
    def __init__(self):
        self.consent_records = {}
    
    def record_consent(self, user_id: str, consent_type: str, granted: bool, 
                      details: Dict[str, Any] = None) -> None:
        """
        Record user consent for a specific type of processing.
        
        Args:
            user_id: User identifier
            consent_type: Type of consent (e.g., 'data_analysis', 'therapist_reports')
            granted: Whether consent was granted
            details: Additional consent details
        """
        if user_id not in self.consent_records:
            self.consent_records[user_id] = {}
        
        self.consent_records[user_id][consent_type] = {
            'granted': granted,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
    
    def check_consent(self, user_id: str, consent_type: str) -> bool:
        """
        Check if user has granted consent for a specific type of processing.
        
        Args:
            user_id: User identifier
            consent_type: Type of consent to check
            
        Returns:
            True if consent granted, False otherwise
        """
        user_consents = self.consent_records.get(user_id, {})
        consent_record = user_consents.get(consent_type, {})
        return consent_record.get('granted', False)
    
    def revoke_consent(self, user_id: str, consent_type: str) -> bool:
        """
        Revoke user consent for a specific type of processing.
        
        Args:
            user_id: User identifier
            consent_type: Type of consent to revoke
            
        Returns:
            True if successfully revoked, False if no consent existed
        """
        if user_id in self.consent_records and consent_type in self.consent_records[user_id]:
            self.consent_records[user_id][consent_type]['granted'] = False
            self.consent_records[user_id][consent_type]['revoked_timestamp'] = datetime.now().isoformat()
            return True
        return False
    
    def get_user_consents(self, user_id: str) -> Dict[str, Any]:
        """
        Get all consent records for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary of consent records
        """
        return self.consent_records.get(user_id, {})


def secure_hash(data: str, salt: str = None) -> str:
    """
    Create a secure hash of data with optional salt.
    
    Args:
        data: Data to hash
        salt: Optional salt for additional security
        
    Returns:
        Hexadecimal hash string
    """
    if salt:
        data = data + salt
    
    return hashlib.sha256(data.encode()).hexdigest()


def data_retention_cleanup(data_store: Dict[str, Any], retention_days: int = 365) -> Dict[str, Any]:
    """
    Clean up data based on retention policies.
    
    Args:
        data_store: Dictionary containing timestamped data
        retention_days: Number of days to retain data
        
    Returns:
        Cleaned data store
    """
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    cleaned_store = {}
    
    for key, value in data_store.items():
        if isinstance(value, dict) and 'timestamp' in value:
            try:
                data_timestamp = datetime.fromisoformat(value['timestamp'].replace('Z', '+00:00'))
                if data_timestamp >= cutoff_date:
                    cleaned_store[key] = value
            except (ValueError, TypeError):
                # Keep data if timestamp parsing fails (safer approach)
                cleaned_store[key] = value
        else:
            # Keep non-timestamped data
            cleaned_store[key] = value
    
    return cleaned_store


# Global privacy audit logger instance
privacy_audit_logger = PrivacyAuditLogger()

# Global consent manager instance
consent_manager = ConsentManager()
