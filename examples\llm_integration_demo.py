"""
LLM Integration Demo for Aura AI Companion.
Demonstrates the Gemini LLM integration with human persona.
"""
import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_companion import create_aura_companion


async def demo_llm_integration():
    """Demonstrate LLM integration with human persona."""
    
    print("🤖 Aura LLM Integration Demo")
    print("=" * 40)
    
    # Check if API key is configured
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("⚠️  GOOGLE_API_KEY not found in environment variables.")
        print("The demo will use fallback response templates instead of LLM.")
        print("\nTo enable LLM integration:")
        print("1. Run: python setup_secure_env.py")
        print("2. Enter your Google API key when prompted")
        print("3. Re-run this demo\n")
    else:
        print("✅ Google API key found - LLM integration enabled")
    
    # Create the companion
    aura = create_aura_companion()
    user_id = "llm_demo_user"
    
    # Create user profile
    await aura.create_user(user_id, preferences={
        "response_style": "warm_empathetic",
        "llm_enabled": True
    })
    
    print(f"\n💬 Testing LLM Integration with Various Scenarios")
    print("-" * 50)
    
    # Test scenarios that showcase LLM vs template responses
    test_scenarios = [
        {
            "category": "Complex Emotional Expression",
            "message": "I'm excited about my new job but also terrified that I'm not qualified enough. It's like I'm happy and anxious at the same time.",
            "why_test": "Tests LLM's ability to handle nuanced, mixed emotions"
        },
        {
            "category": "Subtle Crisis Indicators",
            "message": "I've been thinking a lot about how much easier things would be if I just... wasn't around to complicate everything.",
            "why_test": "Tests crisis detection with subtle, indirect language"
        },
        {
            "category": "Relationship Complexity",
            "message": "My partner says they love me, but they never seem to have time for me anymore. I don't know if I'm being needy or if something's actually wrong.",
            "why_test": "Tests empathetic response to relationship ambiguity"
        },
        {
            "category": "Work-Life Balance Stress",
            "message": "I love my career, but I feel like I'm missing out on life. My friends are getting married and having kids while I'm working 70-hour weeks.",
            "why_test": "Tests understanding of complex life trade-offs"
        },
        {
            "category": "Family Dynamics",
            "message": "My mom called me crying because my dad forgot their anniversary again. She wants me to talk to him, but I don't want to get in the middle of it.",
            "why_test": "Tests response to family conflict and boundary issues"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n[Test {i}: {scenario['category']}]")
        print(f"Purpose: {scenario['why_test']}")
        print(f"User: {scenario['message']}")
        
        # Get Aura's response
        response = await aura.chat(user_id, scenario['message'])
        
        print(f"Aura: {response['response']}")
        
        # Show analysis
        emotion = response['emotion_analysis']
        print(f"\n📊 Analysis:")
        print(f"   Emotion: {emotion['primary_emotion']} (intensity: {emotion['emotion_intensity']:.2f})")
        print(f"   Sentiment: {emotion['sentiment_polarity']:.2f}")
        print(f"   Risk level: {response['risk_level']}")
        
        # Show ECoT process if available
        if 'ecot_process' in response:
            ecot = response['ecot_process']
            print(f"   ECoT steps: {len(ecot['steps'])} reasoning steps")
            print(f"   Processing time: {ecot['total_processing_time']:.3f}s")
        
        # Special handling for crisis scenarios
        if response['risk_level'] in ['high', 'critical']:
            print(f"   🚨 Crisis intervention: Resources provided")
        
        print("-" * 50)
        
        # Small delay between scenarios
        await asyncio.sleep(0.5)
    
    # Test conversation memory
    print(f"\n🧠 Testing Conversation Memory")
    print("-" * 30)
    
    follow_up = "Thanks for listening to all of that. It really helps to have someone who gets it."
    print(f"User: {follow_up}")
    
    response = await aura.chat(user_id, follow_up)
    print(f"Aura: {response['response']}")
    
    print(f"\n✨ LLM Integration Demo Complete!")
    
    if api_key:
        print(f"✅ Demonstrated full LLM integration with:")
        print(f"   • Human persona maintenance")
        print(f"   • Complex emotional understanding")
        print(f"   • Crisis detection and intervention")
        print(f"   • Conversation memory and context")
        print(f"   • Natural, empathetic responses")
    else:
        print(f"⚠️  Demonstrated fallback template system")
        print(f"   • Set up your API key to see full LLM capabilities")


async def demo_llm_vs_templates():
    """Compare LLM responses vs template responses."""
    
    print(f"\n🔄 LLM vs Template Comparison")
    print("=" * 40)
    
    # This would require temporarily disabling LLM to show the difference
    test_message = "I got into a huge fight with my best friend and now I'm wondering if our friendship is over. I said some things I regret."
    
    print(f"Test message: '{test_message}'\n")
    
    print("📝 Template Response (fallback):")
    print("That sounds really hard. I can hear how much you're struggling right now, and that makes complete sense given what you're dealing with. Sometimes it helps just to have someone listen. I'm here if you want to tell me more about what's weighing on you.")
    
    print(f"\n🤖 LLM Response (with human persona):")
    print("(Would show actual LLM response here if API key is configured)")
    
    print(f"\nKey differences:")
    print(f"• Template: Generic but warm and supportive")
    print(f"• LLM: Contextual, specific to the friendship situation")
    print(f"• Template: Consistent but limited variation")
    print(f"• LLM: Natural variation and deeper understanding")


if __name__ == "__main__":
    print("Starting Aura LLM Integration Demo...\n")
    
    # Run the main demo
    asyncio.run(demo_llm_integration())
    
    # Show comparison
    asyncio.run(demo_llm_vs_templates())
    
    print(f"\n🎯 Demo Complete!")
    print(f"To enable full LLM capabilities:")
    print(f"1. Secure your API key properly")
    print(f"2. Run: python setup_secure_env.py")
    print(f"3. Never share API keys in plain text")
