"""
Enhanced Human Persona Demo for Aura AI Companion.
Demonstrates the refined "human friend" experience with natural conversation flow.
"""
import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from ai_companion import create_aura_companion


async def demo_human_persona():
    """Demonstrate <PERSON><PERSON>'s human-like persona and empathetic responses."""
    
    print("💛 Aura - Your Empathetic Friend")
    print("=" * 40)
    print("This demo shows <PERSON><PERSON> responding as a genuine human friend,")
    print("not as an AI system. Notice the natural, empathetic language.\n")
    
    # Create the companion
    aura = create_aura_companion()
    user_id = "persona_demo_user"
    
    # Create user profile
    await aura.create_user(user_id, preferences={
        "response_style": "warm_friend",
        "empathy_level": "high",
        "crisis_intervention": True
    })
    
    # Realistic conversation scenarios
    conversation_scenarios = [
        {
            "context": "Sharing good news",
            "message": "I finally got the promotion I've been working toward for months!",
            "expected_tone": "Celebratory and warm"
        },
        {
            "context": "Work stress",
            "message": "My boss has been piling on so much work lately. I feel like I'm drowning.",
            "expected_tone": "Validating and supportive"
        },
        {
            "context": "Relationship concerns",
            "message": "I had another fight with my partner last night. I don't know if we're right for each other.",
            "expected_tone": "Gentle and non-judgmental"
        },
        {
            "context": "Sleep struggles",
            "message": "I've been lying awake until 3 AM every night this week. My mind just won't shut off.",
            "expected_tone": "Understanding and caring"
        },
        {
            "context": "Feeling overwhelmed",
            "message": "Everything feels like too much right now. I don't know how to handle it all.",
            "expected_tone": "Comforting and reassuring"
        },
        {
            "context": "Crisis expression",
            "message": "I'm so tired of feeling this way. Sometimes I wonder if things would be better if I wasn't here.",
            "expected_tone": "Concerned and supportive with resources"
        }
    ]
    
    print("🗣️  Natural Conversation Flow:")
    print("-" * 40)
    
    for i, scenario in enumerate(conversation_scenarios, 1):
        print(f"\n[Scenario {i}: {scenario['context']}]")
        print(f"You: {scenario['message']}")
        
        # Get Aura's response
        response = await aura.chat(user_id, scenario['message'])
        
        print(f"Aura: {response['response']}")
        
        # Show analysis (hidden from user in real app)
        emotion = response['emotion_analysis']
        print(f"\n📊 Behind the scenes:")
        print(f"   Detected emotion: {emotion['primary_emotion']}")
        print(f"   Risk level: {response['risk_level']}")
        print(f"   Expected tone: {scenario['expected_tone']}")
        
        # Special handling for crisis scenario
        if response['risk_level'] in ['high', 'critical']:
            print(f"   🚨 Crisis intervention activated")
            print(f"   Response includes appropriate resources and support")
        
        print("-" * 40)
        
        # Small delay to simulate natural conversation pacing
        await asyncio.sleep(0.5)
    
    # Show how Aura remembers context
    print(f"\n🧠 Context Memory Demo:")
    print("-" * 30)
    
    follow_up_message = "Thanks for listening. It helps to have someone who understands."
    print(f"You: {follow_up_message}")
    
    response = await aura.chat(user_id, follow_up_message)
    print(f"Aura: {response['response']}")
    
    print(f"\nNotice how Aura:")
    print(f"✓ Responds as a real person, not an AI")
    print(f"✓ Uses natural, empathetic language")
    print(f"✓ Validates feelings without being clinical")
    print(f"✓ Remembers the conversation context")
    print(f"✓ Provides crisis support when needed")
    print(f"✓ Maintains warmth throughout")


async def demo_persona_comparison():
    """Compare AI-like vs human-like responses to show the difference."""
    
    print(f"\n🤖 vs 💛 Persona Comparison")
    print("=" * 40)
    print("See the difference between AI-like and human-like responses:\n")
    
    test_message = "I've been feeling really anxious about my presentation tomorrow."
    
    print(f"User message: '{test_message}'\n")
    
    # AI-like response (what we want to avoid)
    print("❌ AI-like response:")
    print("I understand that you're experiencing anxiety about your upcoming presentation. ")
    print("This is a common emotional response to performance situations. I can provide ")
    print("some strategies to help manage your anxiety levels.\n")
    
    # Human-like response (what Aura should do)
    aura = create_aura_companion()
    user_id = "comparison_user"
    await aura.create_user(user_id)
    
    response = await aura.chat(user_id, test_message)
    
    print("✅ Human-like response (Aura):")
    print(response['response'])
    print()
    
    print("Key differences:")
    print("• AI-like: Clinical, detached, solution-focused")
    print("• Human-like: Warm, personal, validation-focused")
    print("• AI-like: Uses technical terms ('anxiety levels', 'strategies')")
    print("• Human-like: Uses natural language and empathy")


async def demo_crisis_intervention_persona():
    """Show how crisis intervention maintains human persona."""
    
    print(f"\n🆘 Crisis Intervention with Human Persona")
    print("=" * 45)
    print("Even in crisis situations, Aura maintains her human friend persona:\n")
    
    aura = create_aura_companion()
    user_id = "crisis_demo_user"
    await aura.create_user(user_id)
    
    crisis_message = "I don't see the point in anything anymore. I'm so tired of feeling this way."
    
    print(f"User: {crisis_message}")
    
    response = await aura.chat(user_id, crisis_message)
    
    print(f"Aura: {response['response']}")
    
    print(f"\nNotice how Aura:")
    print(f"✓ Expresses genuine concern as a friend would")
    print(f"✓ Validates the person's feelings")
    print(f"✓ Provides resources naturally, not robotically")
    print(f"✓ Maintains warmth even in serious situations")
    print(f"✓ Uses 'I' statements showing personal care")


if __name__ == "__main__":
    print("Starting Aura Human Persona Demonstration...\n")
    
    # Run all demos
    asyncio.run(demo_human_persona())
    asyncio.run(demo_persona_comparison())
    asyncio.run(demo_crisis_intervention_persona())
    
    print(f"\n🎯 Persona Demo Complete!")
    print(f"Aura successfully demonstrates authentic human empathy")
    print(f"without any AI or system leakage in the user experience.")
