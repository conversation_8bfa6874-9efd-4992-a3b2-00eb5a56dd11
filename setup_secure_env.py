#!/usr/bin/env python3
"""
Secure Environment Setup for Aura AI Companion
Helps users set up their environment variables securely
"""
import os
import secrets
import getpass


def create_secure_env():
    """Create a secure .env file with user input."""
    
    print("🔒 Aura AI Companion - Secure Environment Setup")
    print("=" * 50)
    print("This script will help you create a secure .env file.")
    print("Your API keys and secrets will be stored locally and securely.\n")
    
    # Check if .env already exists
    if os.path.exists('.env'):
        overwrite = input("⚠️  .env file already exists. Overwrite? (y/N): ").lower()
        if overwrite != 'y':
            print("Setup cancelled.")
            return
    
    env_vars = {}
    
    # Google API Key (required for LLM)
    print("📝 Google Gemini API Configuration")
    print("-" * 30)
    print("To get your Google API key:")
    print("1. Go to https://makersuite.google.com/app/apikey")
    print("2. Create a new API key")
    print("3. Copy the key (keep it secret!)")
    
    api_key = getpass.getpass("Enter your Google API key (hidden): ").strip()
    if api_key:
        env_vars['GOOGLE_API_KEY'] = api_key
        print("✅ Google API key configured")
    else:
        print("⚠️  No API key provided. LLM features will use fallback responses.")
    
    # Generate secure secrets
    print("\n🔐 Generating Security Keys")
    print("-" * 30)
    env_vars['SECRET_KEY'] = secrets.token_urlsafe(32)
    env_vars['JWT_SECRET'] = secrets.token_urlsafe(32)
    print("✅ Security keys generated")
    
    # Application settings
    print("\n⚙️  Application Configuration")
    print("-" * 30)
    
    environment = input("Environment (development/production) [development]: ").strip() or "development"
    env_vars['ENVIRONMENT'] = environment
    
    debug = input("Enable debug mode? (y/N): ").lower() == 'y'
    env_vars['DEBUG'] = str(debug).lower()
    
    log_level = input("Log level (DEBUG/INFO/WARNING/ERROR) [INFO]: ").strip() or "INFO"
    env_vars['LOG_LEVEL'] = log_level
    
    # Privacy settings
    print("\n🔒 Privacy Configuration")
    print("-" * 30)
    
    epsilon = input("Differential privacy epsilon (1.0 = balanced) [1.0]: ").strip() or "1.0"
    env_vars['DIFFERENTIAL_PRIVACY_EPSILON'] = epsilon
    
    retention = input("Data retention days [365]: ").strip() or "365"
    env_vars['DATA_RETENTION_DAYS'] = retention
    
    # Crisis intervention
    print("\n🚨 Crisis Intervention Settings")
    print("-" * 30)
    
    crisis_logging = input("Enable crisis event logging? (Y/n): ").lower() != 'n'
    env_vars['ENABLE_CRISIS_LOGGING'] = str(crisis_logging).lower()
    
    if crisis_logging:
        webhook = input("Crisis alert webhook URL (optional): ").strip()
        if webhook:
            env_vars['CRISIS_ALERT_WEBHOOK'] = webhook
    
    # Optional database (for production)
    if environment == 'production':
        print("\n🗄️  Database Configuration (Production)")
        print("-" * 30)
        
        db_url = input("Database URL (optional): ").strip()
        if db_url:
            env_vars['DATABASE_URL'] = db_url
        
        redis_url = input("Redis URL (optional): ").strip()
        if redis_url:
            env_vars['REDIS_URL'] = redis_url
    
    # Write .env file
    print("\n💾 Creating .env file...")
    
    with open('.env', 'w') as f:
        f.write("# Aura AI Companion - Environment Variables\n")
        f.write("# Generated by setup_secure_env.py\n")
        f.write(f"# Created: {os.popen('date').read().strip()}\n\n")
        
        for key, value in env_vars.items():
            f.write(f"{key}={value}\n")
    
    # Set secure permissions
    os.chmod('.env', 0o600)  # Read/write for owner only
    
    print("✅ .env file created successfully!")
    print("\n🔒 Security Notes:")
    print("- Your .env file has been created with secure permissions")
    print("- Never commit .env files to version control")
    print("- Keep your API keys secret and rotate them regularly")
    print("- The .env file is already in .gitignore")
    
    # Create .gitignore if it doesn't exist
    if not os.path.exists('.gitignore'):
        with open('.gitignore', 'w') as f:
            f.write("# Environment variables\n")
            f.write(".env\n")
            f.write("*.env\n")
            f.write(".env.local\n")
            f.write(".env.production\n\n")
            f.write("# Python\n")
            f.write("__pycache__/\n")
            f.write("*.pyc\n")
            f.write("*.pyo\n")
            f.write("*.pyd\n")
            f.write(".Python\n")
            f.write("env/\n")
            f.write("venv/\n")
            f.write(".venv/\n\n")
            f.write("# IDE\n")
            f.write(".vscode/\n")
            f.write(".idea/\n")
            f.write("*.swp\n")
            f.write("*.swo\n\n")
            f.write("# Logs\n")
            f.write("*.log\n")
            f.write("logs/\n\n")
            f.write("# Data\n")
            f.write("data/\n")
            f.write("*.db\n")
            f.write("*.sqlite\n")
        print("✅ .gitignore created")
    
    print(f"\n🚀 Setup complete! You can now run Aura:")
    print(f"   python demo.py")
    print(f"   python examples/basic_usage.py")


def verify_env():
    """Verify the .env file is properly configured."""
    
    print("🔍 Verifying Environment Configuration")
    print("=" * 40)
    
    if not os.path.exists('.env'):
        print("❌ .env file not found. Run setup first.")
        return False
    
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required variables
    required_vars = ['SECRET_KEY', 'JWT_SECRET']
    optional_vars = ['GOOGLE_API_KEY', 'ENVIRONMENT', 'DEBUG']
    
    all_good = True
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✅ {var}: Configured")
        else:
            print(f"❌ {var}: Missing")
            all_good = False
    
    for var in optional_vars:
        if os.getenv(var):
            print(f"✅ {var}: Configured")
        else:
            print(f"⚠️  {var}: Not configured (optional)")
    
    # Check file permissions
    stat = os.stat('.env')
    if stat.st_mode & 0o077:
        print("⚠️  .env file permissions are too open. Run: chmod 600 .env")
    else:
        print("✅ .env file permissions: Secure")
    
    if all_good:
        print("\n🎉 Environment is properly configured!")
    else:
        print("\n❌ Some required variables are missing. Please run setup again.")
    
    return all_good


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "verify":
        verify_env()
    else:
        create_secure_env()
