"""
Crisis Service for the Aura AI Companion.
A dedicated service for multi-layered crisis detection and intervention.
"""
import asyncio
import re
from typing import Dict, List, Optional
from datetime import datetime, timedelta

from ..models.data_models import EmotionProfile, RiskLevel


class CrisisService:
    """
    A dedicated service for multi-layered crisis detection and intervention.
    Implements multiple detection mechanisms and appropriate response protocols.
    """
    
    def __init__(self):
        # In a real system, this would load a fine-tuned classification model
        self.crisis_classifier_model = None
        
        # Crisis detection patterns (expanded and more comprehensive)
        self.crisis_patterns = {
            'self_harm': [
                re.compile(r'\b(want to die|kill myself|end it all|not worth living|better off dead)\b', re.IGNORECASE),
                re.compile(r'\b(suicide|suicidal|self harm|hurt myself|cut myself)\b', re.IGNORECASE),
                re.compile(r'\b(overdose|pills|jump|bridge|gun|rope)\b', re.IGNORECASE)
            ],
            'hopelessness': [
                re.compile(r'\b(no point|give up|can\'t go on|hopeless|pointless)\b', re.IGNORECASE),
                re.compile(r'\b(nothing matters|no future|no way out|trapped)\b', re.IGNORECASE),
                re.compile(r'\b(everyone would be better|burden to everyone|waste of space)\b', re.IGNORECASE)
            ],
            'isolation': [
                re.compile(r'\b(no one cares|all alone|nobody understands|isolated)\b', re.IGNORECASE),
                re.compile(r'\b(no friends|no family|abandoned|rejected)\b', re.IGNORECASE)
            ],
            'substance_abuse': [
                re.compile(r'\b(drinking too much|using drugs|high all the time|can\'t stop)\b', re.IGNORECASE),
                re.compile(r'\b(overdosed|blackout|addiction|rehab)\b', re.IGNORECASE)
            ]
        }
        
        # Crisis resources
        self.crisis_resources = {
            'suicide_prevention': {
                'hotline': '988',
                'text': 'Text HOME to 741741',
                'chat': 'suicidepreventionlifeline.org/chat'
            },
            'crisis_text': {
                'number': '741741',
                'keyword': 'HOME'
            },
            'emergency': {
                'number': '911',
                'when_to_call': 'immediate danger'
            }
        }
        
        print("CrisisService Initialized.")

    async def assess_risk(self, message: str, emotion_profile: EmotionProfile, user_ikg_summary: Dict) -> str:
        """
        Assesses mental health risk using a multi-factor approach.
        
        Args:
            message: The user's message text
            emotion_profile: Emotional analysis of the message
            user_ikg_summary: Summary of user's knowledge graph patterns
            
        Returns:
            Risk level as string: 'None', 'Low', 'Medium', 'High', 'Critical'
        """
        risk_score = 0.0
        risk_factors = []
        
        # Factor 1: Direct crisis language detection
        crisis_language_score = self._detect_crisis_language(message)
        risk_score += crisis_language_score
        if crisis_language_score > 0:
            risk_factors.append(f"Crisis language detected (score: {crisis_language_score:.2f})")
        
        # Factor 2: Real-time emotional analysis
        if emotion_profile.is_crisis_signal:
            risk_score += 0.5
            risk_factors.append("Emotion analysis flagged crisis signal")
        
        if emotion_profile.sentiment_polarity < -0.7:
            risk_score += 0.3
            risk_factors.append(f"Very negative sentiment ({emotion_profile.sentiment_polarity:.2f})")
        
        # Factor 3: Latent risk from IKG trends
        ikg_risk_score = self._assess_ikg_risk_factors(user_ikg_summary)
        risk_score += ikg_risk_score
        if ikg_risk_score > 0:
            risk_factors.append(f"Historical pattern concerns (score: {ikg_risk_score:.2f})")
        
        # Factor 4: Emotional intensity and arousal
        if emotion_profile.emotion_intensity > 0.8 and emotion_profile.arousal_level > 0.8:
            risk_score += 0.2
            risk_factors.append("High emotional intensity and arousal")
        
        # Determine risk level
        risk_level = self._calculate_risk_level(risk_score)
        
        # Log risk assessment (in production, this would be secure logging)
        print(f"Risk Assessment: {risk_level} (score: {risk_score:.2f})")
        print(f"Risk Factors: {risk_factors}")
        
        return risk_level

    def _detect_crisis_language(self, message: str) -> float:
        """Detect crisis-related language patterns in the message."""
        crisis_score = 0.0
        detected_patterns = []
        
        for category, patterns in self.crisis_patterns.items():
            for pattern in patterns:
                matches = pattern.findall(message)
                if matches:
                    # Weight different categories differently
                    category_weights = {
                        'self_harm': 0.8,
                        'hopelessness': 0.6,
                        'isolation': 0.3,
                        'substance_abuse': 0.4
                    }
                    
                    category_score = len(matches) * category_weights.get(category, 0.5)
                    crisis_score += category_score
                    detected_patterns.append(f"{category}: {matches}")
        
        # Cap the crisis language score
        crisis_score = min(crisis_score, 1.0)
        
        if detected_patterns:
            print(f"Crisis patterns detected: {detected_patterns}")
        
        return crisis_score

    def _assess_ikg_risk_factors(self, user_ikg_summary: Dict) -> float:
        """Assess risk factors from the user's knowledge graph patterns."""
        ikg_risk_score = 0.0
        
        # Check for concerning trends
        if user_ikg_summary.get("negative_trend_detected"):
            ikg_risk_score += 0.3
        
        if user_ikg_summary.get("recent_mood_decline"):
            ikg_risk_score += 0.2
        
        if user_ikg_summary.get("social_isolation_indicators"):
            ikg_risk_score += 0.2
        
        if user_ikg_summary.get("sleep_pattern_disruption"):
            ikg_risk_score += 0.1
        
        return min(ikg_risk_score, 0.5)  # Cap IKG contribution

    def _calculate_risk_level(self, risk_score: float) -> str:
        """Convert numerical risk score to categorical risk level."""
        if risk_score >= 0.9:
            return RiskLevel.CRITICAL.value
        elif risk_score >= 0.7:
            return RiskLevel.HIGH.value
        elif risk_score >= 0.4:
            return RiskLevel.MEDIUM.value
        elif risk_score >= 0.1:
            return RiskLevel.LOW.value
        else:
            return RiskLevel.NONE.value

    async def get_crisis_intervention_response(self, risk_level: str, detected_factors: List[str] = None) -> Dict[str, any]:
        """
        Generate appropriate crisis intervention response based on risk level.
        
        Args:
            risk_level: The assessed risk level
            detected_factors: List of detected risk factors
            
        Returns:
            Dictionary containing intervention response and resources
        """
        if risk_level == RiskLevel.CRITICAL.value:
            return {
                'immediate_action': True,
                'response': (
                    "I'm very concerned about what you're sharing. Your safety is the most important thing right now. "
                    "Please reach out for immediate help - you don't have to face this alone."
                ),
                'resources': [
                    f"Call 988 (Suicide & Crisis Lifeline) - available 24/7",
                    f"Text HOME to 741741 (Crisis Text Line)",
                    f"If you're in immediate danger, call 911",
                    f"Go to your nearest emergency room"
                ],
                'follow_up_required': True
            }
        
        elif risk_level == RiskLevel.HIGH.value:
            return {
                'immediate_action': False,
                'response': (
                    "I can hear that you're going through something really difficult right now. "
                    "It's important that you have support during this time. Please consider reaching out to a professional who can help."
                ),
                'resources': [
                    f"Call 988 (Suicide & Crisis Lifeline) for support",
                    f"Text HOME to 741741 if you prefer texting",
                    f"Consider contacting a mental health professional",
                    f"Reach out to a trusted friend or family member"
                ],
                'follow_up_required': True
            }
        
        elif risk_level == RiskLevel.MEDIUM.value:
            return {
                'immediate_action': False,
                'response': (
                    "It sounds like you're dealing with some challenging feelings. "
                    "Sometimes it can help to talk to someone about what you're experiencing."
                ),
                'resources': [
                    f"Consider talking to a counselor or therapist",
                    f"988 Lifeline is available if you need someone to talk to",
                    f"Connect with supportive friends or family",
                    f"Practice self-care activities that help you feel better"
                ],
                'follow_up_required': False
            }
        
        else:
            return {
                'immediate_action': False,
                'response': None,
                'resources': [],
                'follow_up_required': False
            }

    async def log_crisis_event(self, user_id: str, risk_level: str, message: str, intervention_provided: Dict) -> None:
        """
        Log crisis events for monitoring and follow-up.
        In production, this would integrate with secure logging and alert systems.
        
        Args:
            user_id: User identifier
            risk_level: Assessed risk level
            message: The concerning message
            intervention_provided: The intervention response provided
        """
        crisis_log = {
            'timestamp': datetime.now().isoformat(),
            'user_id': user_id,
            'risk_level': risk_level,
            'message_excerpt': message[:100] + "..." if len(message) > 100 else message,
            'intervention_provided': intervention_provided,
            'requires_follow_up': intervention_provided.get('follow_up_required', False)
        }
        
        # In production, this would be sent to secure logging system
        print(f"CRISIS EVENT LOGGED: {crisis_log}")
        
        # If critical, would trigger immediate alerts to human moderators
        if risk_level == RiskLevel.CRITICAL.value:
            await self._trigger_emergency_alert(user_id, crisis_log)

    async def _trigger_emergency_alert(self, user_id: str, crisis_log: Dict) -> None:
        """
        Trigger emergency alerts for critical risk situations.
        In production, this would alert human moderators and crisis intervention teams.
        """
        print(f"EMERGENCY ALERT TRIGGERED for user {user_id}")
        print(f"Crisis details: {crisis_log}")
        
        # In production, this would:
        # 1. Alert human crisis intervention specialists
        # 2. Potentially contact emergency services if user has provided consent
        # 3. Escalate to appropriate mental health professionals
        # 4. Follow legal and ethical protocols for crisis intervention

    def get_crisis_resources(self) -> Dict[str, any]:
        """Get available crisis resources."""
        return self.crisis_resources

    async def validate_crisis_response(self, response: str) -> Dict[str, any]:
        """
        Validate that a crisis response is appropriate and helpful.
        
        Args:
            response: The response to validate
            
        Returns:
            Validation results
        """
        validation_checks = {
            'contains_resources': any(resource in response.lower() for resource in ['988', '741741', 'help', 'support']),
            'empathetic_tone': any(word in response.lower() for word in ['understand', 'hear', 'concerned', 'care']),
            'no_dismissive_language': not any(phrase in response.lower() for phrase in ['just', 'simply', 'calm down', 'get over']),
            'appropriate_urgency': 'immediate' in response.lower() or 'right now' in response.lower(),
            'professional_referral': any(word in response.lower() for word in ['professional', 'counselor', 'therapist', 'help'])
        }
        
        validation_score = sum(validation_checks.values()) / len(validation_checks)
        
        return {
            'validation_score': validation_score,
            'checks': validation_checks,
            'is_appropriate': validation_score >= 0.6
        }
