"""
Main Conversation Service for the Aura AI Companion.
Orchestrates the complete conversation flow including emotion analysis, inference, and response generation.
"""
import asyncio
import uuid
from typing import List, Dict, Optional
from datetime import datetime

from ..models.data_models import ConversationMessage, EmotionProfile, UserProfile
from ..emotion.emotion_analysis_service import EmotionAnalysisService
from ..inference.inference_engine import InferenceEngine
from ..memory.knowledge_graph_service import KnowledgeGraphService
from ..conversation.ecot_service import ECoTService
from ..safety.crisis_service import CrisisService


class ConversationService:
    """
    Main orchestrator for conversation processing in the Aura AI Companion.
    Coordinates emotion analysis, inference, memory updates, and response generation.
    """
    
    def __init__(self):
        # Initialize core services
        self.emotion_service = EmotionAnalysisService()
        self.inference_engine = InferenceEngine()
        self.knowledge_service = KnowledgeGraphService()
        self.ecot_service = ECoTService(self.emotion_service, self.knowledge_service)
        self.crisis_service = CrisisService()
        
        # Conversation storage (in production, this would be a database)
        self.conversations: Dict[str, List[ConversationMessage]] = {}
        self.user_profiles: Dict[str, UserProfile] = {}
        
        print("ConversationService initialized with all components.")

    async def process_user_message(self, user_id: str, message_text: str) -> Dict[str, any]:
        """
        Process a user message through the complete Aura pipeline.
        
        Args:
            user_id: Unique identifier for the user
            message_text: The user's message text
            
        Returns:
            Dictionary containing the response and processing metadata
        """
        start_time = datetime.now()
        
        # Create conversation message
        message = ConversationMessage(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            sender='user',
            message=message_text
        )
        
        # Step 1: Emotion Analysis
        emotion_profile = await self.emotion_service.analyze_emotion(message_text)
        message.emotion_profile = emotion_profile
        
        # Step 2: Crisis Assessment
        risk_level = await self._assess_crisis_risk(user_id, message)
        
        # Step 3: Get conversation history
        conversation_history = self.conversations.get(user_id, [])
        
        # Step 4: Process through ECoT
        ecot_process = await self.ecot_service.process_message_with_ecot(message, conversation_history)
        
        # Step 5: Store message in conversation history
        if user_id not in self.conversations:
            self.conversations[user_id] = []
        self.conversations[user_id].append(message)
        
        # Step 6: Run inference on updated conversation
        await self._update_user_insights(user_id)
        
        # Step 7: Create assistant response message
        assistant_message = ConversationMessage(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            sender='assistant',
            message=ecot_process.final_response
        )
        self.conversations[user_id].append(assistant_message)
        
        # Step 8: Calculate processing metrics
        end_time = datetime.now()
        total_processing_time = (end_time - start_time).total_seconds()
        
        return {
            'response': ecot_process.final_response,
            'emotion_analysis': emotion_profile.to_dict(),
            'risk_level': risk_level,
            'ecot_process': ecot_process.to_dict(),
            'processing_time': total_processing_time,
            'conversation_length': len(self.conversations[user_id])
        }

    async def _assess_crisis_risk(self, user_id: str, message: ConversationMessage) -> str:
        """Assess crisis risk for the user's message."""
        # Get user's IKG summary for context
        user_ikg_summary = await self._get_user_ikg_summary(user_id)
        
        # Assess risk using crisis service
        risk_level = await self.crisis_service.assess_risk(
            message.message,
            message.emotion_profile,
            user_ikg_summary
        )
        
        return risk_level

    async def _get_user_ikg_summary(self, user_id: str) -> Dict[str, any]:
        """Get a summary of the user's knowledge graph for risk assessment."""
        # In a real implementation, this would query the knowledge graph
        # for patterns that might indicate mental health trends
        return {
            'negative_trend_detected': False,  # Placeholder
            'recent_mood_decline': False,
            'social_isolation_indicators': False,
            'sleep_pattern_disruption': False
        }

    async def _update_user_insights(self, user_id: str) -> None:
        """Update user insights based on the latest conversation."""
        conversation_history = self.conversations.get(user_id, [])
        
        if len(conversation_history) >= 5:  # Only run inference with sufficient data
            # Run inference on recent messages
            recent_messages = conversation_history[-20:]  # Last 20 messages
            insights = await self.inference_engine.infer_insights_from_transcript(recent_messages)
            
            # Update knowledge graph with new insights
            if insights:
                await self.knowledge_service.update_graph_with_insights(insights)
                print(f"Updated knowledge graph with {len(insights)} new insights for user {user_id}")

    async def get_conversation_history(self, user_id: str, limit: int = 50) -> List[Dict[str, any]]:
        """
        Get conversation history for a user.
        
        Args:
            user_id: User identifier
            limit: Maximum number of messages to return
            
        Returns:
            List of conversation messages as dictionaries
        """
        conversation = self.conversations.get(user_id, [])
        recent_messages = conversation[-limit:] if len(conversation) > limit else conversation
        
        return [msg.to_dict() for msg in recent_messages]

    async def get_user_insights_summary(self, user_id: str) -> Dict[str, any]:
        """
        Get a summary of insights about the user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing user insights and patterns
        """
        conversation_history = self.conversations.get(user_id, [])
        
        if not conversation_history:
            return {'message': 'No conversation history available'}
        
        # Analyze conversation trends
        user_messages = [msg for msg in conversation_history if msg.sender == 'user']
        trend_analysis = await self.emotion_service.analyze_conversation_trend(user_messages)
        
        # Get recent insights
        recent_insights = await self.inference_engine.infer_insights_from_transcript(user_messages[-50:])
        
        # Format insights summary
        insights_summary = {
            'conversation_stats': {
                'total_messages': len(conversation_history),
                'user_messages': len(user_messages),
                'conversation_span_days': self._calculate_conversation_span(conversation_history)
            },
            'emotional_trends': trend_analysis,
            'recent_insights': [insight.to_dict() for insight in recent_insights],
            'knowledge_graph_stats': {
                'total_nodes': len(self.knowledge_service.nodes),
                'total_edges': len(self.knowledge_service.edges)
            }
        }
        
        return insights_summary

    def _calculate_conversation_span(self, messages: List[ConversationMessage]) -> int:
        """Calculate the span of conversation in days."""
        if len(messages) < 2:
            return 0
        
        earliest = min(msg.timestamp for msg in messages)
        latest = max(msg.timestamp for msg in messages)
        
        return (latest - earliest).days

    async def create_user_profile(self, user_id: str, preferences: Dict[str, any] = None, 
                                privacy_settings: Dict[str, bool] = None) -> UserProfile:
        """
        Create a new user profile.
        
        Args:
            user_id: Unique user identifier
            preferences: User preferences dictionary
            privacy_settings: Privacy settings dictionary
            
        Returns:
            Created UserProfile object
        """
        profile = UserProfile(
            user_id=user_id,
            preferences=preferences or {},
            privacy_settings=privacy_settings or {
                'allow_insights_generation': True,
                'allow_therapist_reports': False,
                'data_retention_days': 365
            },
            crisis_contacts=[],
            therapist_info=None,
            created_at=datetime.now(),
            last_active=datetime.now()
        )
        
        self.user_profiles[user_id] = profile
        return profile

    async def update_user_activity(self, user_id: str) -> None:
        """Update the user's last active timestamp."""
        if user_id in self.user_profiles:
            self.user_profiles[user_id].last_active = datetime.now()

    async def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get a user's profile."""
        return self.user_profiles.get(user_id)

    async def clear_conversation_history(self, user_id: str) -> bool:
        """
        Clear conversation history for a user (respecting privacy settings).
        
        Args:
            user_id: User identifier
            
        Returns:
            True if successful, False otherwise
        """
        if user_id in self.conversations:
            del self.conversations[user_id]
            print(f"Cleared conversation history for user {user_id}")
            return True
        return False

    async def export_user_data(self, user_id: str) -> Dict[str, any]:
        """
        Export all user data for privacy compliance (GDPR, etc.).
        
        Args:
            user_id: User identifier
            
        Returns:
            Dictionary containing all user data
        """
        conversation_history = self.conversations.get(user_id, [])
        user_profile = self.user_profiles.get(user_id)
        insights_summary = await self.get_user_insights_summary(user_id)
        
        return {
            'user_profile': user_profile.to_dict() if user_profile else None,
            'conversation_history': [msg.to_dict() for msg in conversation_history],
            'insights_summary': insights_summary,
            'export_timestamp': datetime.now().isoformat()
        }
