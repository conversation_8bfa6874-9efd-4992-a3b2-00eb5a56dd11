#!/usr/bin/env python3
"""
Simple Gradio test to verify the interface works.
"""
import gradio as gr
import sys
import os
import asyncio

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Global variables
aura_companion = None

def initialize_aura():
    """Initialize the Aura AI Companion."""
    global aura_companion
    if aura_companion is None:
        try:
            from ai_companion import create_aura_companion
            aura_companion = create_aura_companion()
            # Create user profile
            asyncio.run(aura_companion.create_user("gradio_user"))
            return "✅ Aura initialized successfully!"
        except Exception as e:
            return f"❌ Error initializing Aura: {str(e)}"
    return "✅ Aura already initialized!"

async def chat_async(message):
    """Async chat function."""
    global aura_companion
    if aura_companion is None:
        return "Please initialize Aura first."
    
    try:
        response = await aura_companion.chat("gradio_user", message)
        return response['response']
    except Exception as e:
        return f"Error: {str(e)}"

def chat_sync(message):
    """Sync wrapper for chat."""
    if not message.strip():
        return "Please enter a message."
    
    try:
        return asyncio.run(chat_async(message))
    except Exception as e:
        return f"Error: {str(e)}"

# Create the interface
def create_interface():
    with gr.Blocks(title="Aura AI Companion - Simple Test") as interface:
        gr.Markdown("# 🌟 Aura AI Companion - Simple Test")
        
        with gr.Row():
            init_btn = gr.Button("Initialize Aura", variant="primary")
            init_output = gr.Textbox(label="Initialization Status", interactive=False)
        
        gr.Markdown("## Chat with Aura")
        
        with gr.Row():
            message_input = gr.Textbox(label="Your Message", placeholder="Type your message here...")
            send_btn = gr.Button("Send", variant="primary")
        
        response_output = gr.Textbox(label="Aura's Response", interactive=False, lines=3)
        
        # Event handlers
        init_btn.click(initialize_aura, outputs=init_output)
        send_btn.click(chat_sync, inputs=message_input, outputs=response_output)
        message_input.submit(chat_sync, inputs=message_input, outputs=response_output)
    
    return interface

if __name__ == "__main__":
    print("🌟 Starting Simple Aura Gradio Test...")
    
    interface = create_interface()
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
