# 🌟 Aura AI Companion - Web Interface Setup

This guide helps you set up and run the Gradio web interface for Aura AI Companion.

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)

```bash
# Run the setup script
python run_gradio_app.py
```

This will:
- Install required packages automatically
- Create .env file template
- Launch the web interface at http://localhost:7860

### Option 2: Manual Setup

```bash
# Install Gradio and essential packages
pip install gradio>=4.0.0 google-generativeai>=0.3.0 python-dotenv>=1.0.0

# Run the Gradio app
python gradio_app.py
```

## 🔑 API Key Setup (Optional but Recommended)

For full LLM capabilities with natural responses:

1. **Get Google API Key:**
   - Visit: https://makersuite.google.com/app/apikey
   - Create a new API key

2. **Configure Environment:**
   ```bash
   # Create .env file
   echo "GOOGLE_API_KEY=your_api_key_here" > .env
   ```

3. **Restart the application**

**Note:** Without an API key, Aura will use fallback response templates.

## 🌐 Using the Web Interface

### Chat Features
- **💬 Real-time Chat:** Type messages and get empathetic responses
- **📊 Emotion Analysis:** See real-time emotional analysis of your messages
- **🚨 Crisis Detection:** Automatic detection of crisis signals with appropriate support

### Privacy & Insights
- **🧠 Behavioral Insights:** View detected patterns in your conversations
- **📁 Data Export:** Export all your data for privacy compliance
- **🗑️ Data Deletion:** Clear all your data at any time

### Interface Layout
```
┌─────────────────────┬─────────────────┐
│                     │                 │
│   💬 Chat Window    │  📊 Analysis    │
│                     │                 │
│   [Message Input]   │  🧠 Insights    │
│   [Send Button]     │                 │
│                     │  🔒 Privacy     │
│   🗑️ Clear Chat     │     Controls    │
│                     │                 │
└─────────────────────┴─────────────────┘
```

## 🛠️ Troubleshooting

### Common Issues

**"Module not found" errors:**
```bash
pip install gradio google-generativeai python-dotenv
```

**"GOOGLE_API_KEY not found" warning:**
- This is normal - Aura will use fallback responses
- Add your API key to .env file for full capabilities

**Port already in use:**
- The app runs on port 7860 by default
- Close other applications using this port, or modify the port in gradio_app.py

**Slow responses:**
- First response may be slower as Aura initializes
- Subsequent responses should be faster

### Advanced Configuration

**Change Port:**
Edit `gradio_app.py` and modify:
```python
interface.launch(server_port=8080)  # Change to your preferred port
```

**Enable Public Access:**
Edit `gradio_app.py` and modify:
```python
interface.launch(share=True)  # Creates public URL
```

## 🔒 Privacy & Security

- **Local Processing:** All conversations are processed locally
- **No Data Sharing:** Your data is never shared without explicit consent
- **Data Control:** Export or delete your data at any time
- **Crisis Support:** Automatic detection with appropriate resource suggestions

## 📱 Mobile Support

The Gradio interface is mobile-friendly and works on:
- 📱 Smartphones
- 📱 Tablets  
- 💻 Desktop browsers

## 🆘 Getting Help

If you encounter issues:

1. **Check the console output** for error messages
2. **Verify your .env file** has the correct API key format
3. **Try the fallback mode** (without API key) to test basic functionality
4. **Check the main README.md** for additional troubleshooting

## 🎯 What's Next?

Once you have the interface running:

1. **Start chatting** with Aura about your day
2. **Explore the emotion analysis** in real-time
3. **Check behavioral insights** after several conversations
4. **Try the privacy features** (export/delete data)

Enjoy your conversations with Aura! 🌟
