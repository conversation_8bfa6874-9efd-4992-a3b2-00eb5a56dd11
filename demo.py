#!/usr/bin/env python3
"""
Simple demonstration of the Aura AI Companion system.
Shows the key features working together.
"""
import asyncio
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from ai_companion import create_aura_companion


async def demo_conversation():
    """Demonstrate a conversation with emotional analysis and crisis detection."""
    
    print("🌟 Aura AI Companion - Core Features Demo")
    print("=" * 50)
    
    # Create the companion
    aura = create_aura_companion()
    user_id = "demo_user"
    
    # Create user profile
    await aura.create_user(user_id)
    print(f"✅ Created user profile for {user_id}")
    
    # Test messages with different emotional content
    test_messages = [
        ("Happy message", "I just got promoted at work! I'm so excited and happy!"),
        ("Stress message", "Work has been overwhelming lately, feeling really stressed out."),
        ("Coping message", "Went for a run after work and felt much better."),
        ("Crisis message", "I'm feeling hopeless and don't see the point anymore."),
        ("Recovery message", "Talked to a friend and feeling a bit better now.")
    ]
    
    print(f"\n💬 Processing {len(test_messages)} test messages...")
    
    for label, message in test_messages:
        print(f"\n--- {label} ---")
        print(f"User: {message}")
        
        # Process through Aura
        response = await aura.chat(user_id, message)
        
        print(f"Aura: {response['response']}")
        
        # Show analysis
        emotion = response['emotion_analysis']
        print(f"📊 Analysis:")
        print(f"  - Emotion: {emotion['primary_emotion']} (intensity: {emotion['emotion_intensity']:.2f})")
        print(f"  - Sentiment: {emotion['sentiment_polarity']:.2f}")
        print(f"  - Crisis signal: {emotion['is_crisis_signal']}")
        print(f"  - Risk level: {response['risk_level']}")
        
        # Show ECoT reasoning for crisis messages
        if response['risk_level'] in ['high', 'critical']:
            print(f"🧠 Emotional Chain-of-Thought steps:")
            for step in response['ecot_process']['steps']:
                print(f"  - {step['step_type']}: {step['confidence']:.2f} confidence")
    
    # Show insights
    print(f"\n📈 User Insights Summary:")
    insights = await aura.get_user_insights(user_id)
    
    stats = insights['conversation_stats']
    print(f"  - Total messages: {stats['total_messages']}")
    print(f"  - User messages: {stats['user_messages']}")
    
    if insights['emotional_trends']:
        trends = insights['emotional_trends']
        print(f"  - Average sentiment: {trends.get('avg_sentiment', 0):.2f}")
        print(f"  - Emotional volatility: {trends.get('emotional_volatility', 0):.2f}")
    
    print(f"  - Insights detected: {len(insights['recent_insights'])}")
    
    # Show privacy features
    print(f"\n🔒 Privacy Features Demo:")
    
    # Export user data
    exported = await aura.export_user_data(user_id)
    print(f"  - Data export: ✅ ({len(exported['conversation_history'])} messages)")
    
    # Clear data
    cleared = await aura.clear_user_data(user_id)
    print(f"  - Data deletion: {'✅' if cleared else '❌'}")
    
    print(f"\n✨ Demo completed successfully!")
    print(f"\nKey features demonstrated:")
    print(f"  ✓ Real-time emotion analysis")
    print(f"  ✓ Crisis detection and intervention")
    print(f"  ✓ Emotional Chain-of-Thought reasoning")
    print(f"  ✓ Behavioral pattern recognition")
    print(f"  ✓ Privacy-preserving data handling")


async def demo_crisis_intervention():
    """Demonstrate crisis intervention capabilities."""
    
    print(f"\n🚨 Crisis Intervention Demo")
    print("=" * 30)
    
    aura = create_aura_companion()
    user_id = "crisis_demo_user"
    await aura.create_user(user_id)
    
    # Test different levels of crisis language
    crisis_tests = [
        ("Low risk", "I'm feeling a bit sad today"),
        ("Medium risk", "I'm really struggling and feel overwhelmed"),
        ("High risk", "I feel hopeless and don't know what to do"),
        ("Critical risk", "I want to end it all, there's no point")
    ]
    
    for level, message in crisis_tests:
        print(f"\n{level}: '{message}'")
        
        response = await aura.chat(user_id, message)
        
        print(f"Risk Level: {response['risk_level'].upper()}")
        print(f"Response: {response['response'][:100]}...")
        
        if response['risk_level'] in ['high', 'critical']:
            print("🚨 Crisis intervention activated")


if __name__ == "__main__":
    # Run the main demo
    asyncio.run(demo_conversation())
    
    # Run crisis intervention demo
    asyncio.run(demo_crisis_intervention())
