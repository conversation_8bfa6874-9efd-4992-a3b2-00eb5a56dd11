"""
Knowledge Graph Service for the Aura AI Companion.
Manages the long-term Semantic Memory (Implicit Knowledge Graph).
Interfaces with a graph database (e.g., Neo4j, or simulated with dictionaries for PoC).
"""
import asyncio
import uuid
from typing import List, Dict, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from collections import defaultdict
import numpy as np

from ..models.data_models import IKGNode, IKGEdge, InferredInsight, ConversationMessage


class KnowledgeGraphService:
    """
    Manages the long-term Semantic Memory (Implicit Knowledge Graph).
    Interfaces with a graph database (e.g., Neo4j, or simulated with dictionaries for PoC).
    """
    
    def __init__(self):
        self.nodes: Dict[str, IKGNode] = {}
        self.edges: List[IKGEdge] = []
        self.node_embeddings: Dict[str, List[float]] = {}
        self.edge_index: Dict[str, List[str]] = defaultdict(list)  # node_id -> list of connected edge_ids
        print("KnowledgeGraphService Initialized.")

    async def update_graph_with_insights(self, insights: List[InferredInsight]) -> None:
        """
        Takes inferred insights and translates them into IKG nodes and edges.
        This is the bridge between raw inference and structured memory.
        
        Args:
            insights: List of InferredInsight objects to process
        """
        for insight in insights:
            await self._process_insight(insight)
        
        print(f"KnowledgeGraphService: Updated graph with {len(insights)} new insights.")

    async def _process_insight(self, insight: InferredInsight) -> None:
        """Process a single insight and update the graph accordingly."""
        
        if insight.insight_type == 'PotentialSleepTime':
            await self._process_sleep_insight(insight)
        elif insight.insight_type == 'PotentialWakeTime':
            await self._process_wake_insight(insight)
        elif insight.insight_type == 'PositiveCopingMechanism':
            await self._process_coping_insight(insight)
        elif insight.insight_type == 'RelationshipSentiment':
            await self._process_relationship_insight(insight)
        elif insight.insight_type == 'DailyRoutine':
            await self._process_routine_insight(insight)
        elif insight.insight_type == 'WeeklyMoodPattern':
            await self._process_mood_insight(insight)

    async def _process_routine_insight(self, insight: InferredInsight) -> None:
        """Process daily routine insights."""
        activity_node_id = f"routine_{insight.data['activity']}"

        # Create or update routine node
        if activity_node_id in self.nodes:
            node = self.nodes[activity_node_id]
            node.properties.update({
                'average_hour': insight.data['average_hour'],
                'consistency_score': insight.data['consistency_score'],
                'frequency': insight.data['frequency'],
                'weekday_pattern': insight.data['weekday_pattern']
            })
            node.strength = min(1.0, node.strength + 0.1)
            node.last_updated = datetime.now()
        else:
            node = IKGNode(
                node_id=activity_node_id,
                node_type='DailyRoutine',
                properties={
                    'activity': insight.data['activity'],
                    'average_hour': insight.data['average_hour'],
                    'consistency_score': insight.data['consistency_score'],
                    'frequency': insight.data['frequency'],
                    'weekday_pattern': insight.data['weekday_pattern']
                },
                embedding_vector=self._generate_embedding(f"routine {insight.data['activity']}"),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=insight.confidence_score
            )
            self.nodes[activity_node_id] = node

    async def _process_mood_insight(self, insight: InferredInsight) -> None:
        """Process mood pattern insights."""
        mood_node_id = "user_mood_pattern"

        # Create or update mood pattern node
        if mood_node_id in self.nodes:
            node = self.nodes[mood_node_id]
            node.properties.update({
                'weekday_moods': insight.data['weekday_moods'],
                'mood_variance': insight.data['mood_variance'],
                'best_day': insight.data['best_day'],
                'worst_day': insight.data['worst_day']
            })
            node.strength = min(1.0, node.strength + 0.1)
            node.last_updated = datetime.now()
        else:
            node = IKGNode(
                node_id=mood_node_id,
                node_type='MoodPattern',
                properties={
                    'weekday_moods': insight.data['weekday_moods'],
                    'mood_variance': insight.data['mood_variance'],
                    'best_day': insight.data['best_day'],
                    'worst_day': insight.data['worst_day']
                },
                embedding_vector=self._generate_embedding('mood pattern weekly'),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=insight.confidence_score
            )
            self.nodes[mood_node_id] = node

    async def _process_sleep_insight(self, insight: InferredInsight) -> None:
        """Process sleep pattern insights."""
        # Create or update sleep pattern node
        sleep_node_id = "user_sleep_pattern"
        
        if sleep_node_id in self.nodes:
            # Update existing node
            node = self.nodes[sleep_node_id]
            node.properties.update({
                'average_sleep_hour': insight.data['average_sleep_hour'],
                'pattern_consistency': insight.data['pattern_consistency'],
                'data_points': insight.data['sleep_time_count']
            })
            node.strength = min(1.0, node.strength + 0.1)
            node.last_updated = datetime.now()
        else:
            # Create new node
            node = IKGNode(
                node_id=sleep_node_id,
                node_type='SleepPattern',
                properties={
                    'average_sleep_hour': insight.data['average_sleep_hour'],
                    'pattern_consistency': insight.data['pattern_consistency'],
                    'data_points': insight.data['sleep_time_count']
                },
                embedding_vector=self._generate_embedding('sleep_pattern'),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=insight.confidence_score
            )
            self.nodes[sleep_node_id] = node

    async def _process_coping_insight(self, insight: InferredInsight) -> None:
        """Process coping mechanism insights."""
        # Create nodes for trigger, mechanism, and outcome
        trigger_node_id = f"trigger_{insight.data['trigger']}"
        mechanism_node_id = f"mechanism_{insight.data['mechanism']}"
        
        # Create or update trigger node
        if trigger_node_id not in self.nodes:
            self.nodes[trigger_node_id] = IKGNode(
                node_id=trigger_node_id,
                node_type='EmotionalTrigger',
                properties={'trigger_type': insight.data['trigger']},
                embedding_vector=self._generate_embedding(insight.data['trigger']),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=0.5
            )
        
        # Create or update mechanism node
        if mechanism_node_id not in self.nodes:
            self.nodes[mechanism_node_id] = IKGNode(
                node_id=mechanism_node_id,
                node_type='CopingMechanism',
                properties={'mechanism_type': insight.data['mechanism']},
                embedding_vector=self._generate_embedding(insight.data['mechanism']),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=0.5
            )
        
        # Create edge between trigger and mechanism
        edge_id = f"{trigger_node_id}_helps_with_{mechanism_node_id}"
        edge = IKGEdge(
            edge_id=edge_id,
            source_node_id=trigger_node_id,
            target_node_id=mechanism_node_id,
            relationship_type='helps_with',
            weight=insight.confidence_score,
            properties={
                'effectiveness': insight.data['improvement_score'],
                'time_to_coping': insight.data['time_to_coping']
            },
            creation_timestamp=datetime.now(),
            last_updated=datetime.now()
        )
        
        self.edges.append(edge)
        self.edge_index[trigger_node_id].append(edge_id)
        self.edge_index[mechanism_node_id].append(edge_id)

    async def _process_relationship_insight(self, insight: InferredInsight) -> None:
        """Process relationship sentiment insights."""
        person_node_id = f"person_{insight.data['name'].lower()}"
        
        # Create or update person node
        if person_node_id in self.nodes:
            node = self.nodes[person_node_id]
            node.properties.update({
                'avg_sentiment': insight.data['avg_sentiment'],
                'mentions': insight.data['mentions'],
                'relationship_type': insight.data['relationship_type']
            })
            node.strength = min(1.0, node.strength + 0.1)
            node.last_updated = datetime.now()
        else:
            node = IKGNode(
                node_id=person_node_id,
                node_type='Person',
                properties={
                    'name': insight.data['name'],
                    'avg_sentiment': insight.data['avg_sentiment'],
                    'mentions': insight.data['mentions'],
                    'relationship_type': insight.data['relationship_type']
                },
                embedding_vector=self._generate_embedding(f"person {insight.data['name']}"),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=insight.confidence_score
            )
            self.nodes[person_node_id] = node
        
        # Create edge from user to person
        user_node_id = "user_self"
        if user_node_id not in self.nodes:
            self.nodes[user_node_id] = IKGNode(
                node_id=user_node_id,
                node_type='User',
                properties={'is_primary_user': True},
                embedding_vector=self._generate_embedding('user self'),
                creation_timestamp=datetime.now(),
                last_updated=datetime.now(),
                strength=1.0
            )
        
        edge_id = f"{user_node_id}_relationship_{person_node_id}"
        edge = IKGEdge(
            edge_id=edge_id,
            source_node_id=user_node_id,
            target_node_id=person_node_id,
            relationship_type='has_relationship_with',
            weight=abs(insight.data['avg_sentiment']),
            properties={
                'sentiment_polarity': insight.data['avg_sentiment'],
                'relationship_type': insight.data['relationship_type']
            },
            creation_timestamp=datetime.now(),
            last_updated=datetime.now()
        )
        
        # Update or add edge
        existing_edge = self._find_edge(user_node_id, person_node_id, 'has_relationship_with')
        if existing_edge:
            existing_edge.weight = abs(insight.data['avg_sentiment'])
            existing_edge.properties.update(edge.properties)
            existing_edge.last_updated = datetime.now()
        else:
            self.edges.append(edge)
            self.edge_index[user_node_id].append(edge_id)
            self.edge_index[person_node_id].append(edge_id)

    async def query_for_context(self, query_vector: List[float], topic: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        Retrieves rich context for the ECoT prompt.
        1. Performs a vector search to find relevant seed nodes.
        2. Performs a graph traversal (e.g., 2-hop) from those seed nodes to gather related context.
        3. Returns a structured dictionary of this context.
        
        Args:
            query_vector: Embedding vector for the query
            topic: Topic string for additional context
            max_results: Maximum number of context items to return
            
        Returns:
            List of context dictionaries
        """
        # Find relevant nodes using vector similarity
        relevant_nodes = await self._vector_search(query_vector, max_results // 2)
        
        # Expand context through graph traversal
        expanded_context = await self._expand_context(relevant_nodes, max_hops=2)
        
        # Format context for ECoT
        formatted_context = []
        for node_id, relevance_score in expanded_context[:max_results]:
            node = self.nodes[node_id]
            context_item = {
                'node_type': node.node_type,
                'properties': node.properties,
                'relevance_score': relevance_score,
                'strength': node.strength,
                'connected_concepts': await self._get_connected_concepts(node_id)
            }
            formatted_context.append(context_item)
        
        print(f"KnowledgeGraphService: Retrieved {len(formatted_context)} context items for topic '{topic}'.")
        return formatted_context

    async def _vector_search(self, query_vector: List[float], max_results: int) -> List[Tuple[str, float]]:
        """Perform vector similarity search on nodes."""
        similarities = []
        
        for node_id, node in self.nodes.items():
            similarity = self._cosine_similarity(query_vector, node.embedding_vector)
            similarities.append((node_id, similarity))
        
        # Sort by similarity and return top results
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:max_results]

    async def _expand_context(self, seed_nodes: List[Tuple[str, float]], max_hops: int) -> List[Tuple[str, float]]:
        """Expand context through graph traversal."""
        visited = set()
        context_nodes = {}
        
        # Add seed nodes
        for node_id, relevance in seed_nodes:
            context_nodes[node_id] = relevance
            visited.add(node_id)
        
        # Perform breadth-first traversal
        current_nodes = [node_id for node_id, _ in seed_nodes]
        
        for hop in range(max_hops):
            next_nodes = []
            
            for node_id in current_nodes:
                # Get connected nodes
                connected_edges = self.edge_index.get(node_id, [])
                
                for edge_id in connected_edges:
                    edge = self._find_edge_by_id(edge_id)
                    if not edge:
                        continue
                    
                    # Get the other node in the edge
                    other_node_id = edge.target_node_id if edge.source_node_id == node_id else edge.source_node_id
                    
                    if other_node_id not in visited:
                        # Calculate relevance based on edge weight and distance
                        base_relevance = context_nodes.get(node_id, 0.0)
                        propagated_relevance = base_relevance * edge.weight * (0.7 ** hop)  # Decay with distance
                        
                        context_nodes[other_node_id] = max(
                            context_nodes.get(other_node_id, 0.0),
                            propagated_relevance
                        )
                        visited.add(other_node_id)
                        next_nodes.append(other_node_id)
            
            current_nodes = next_nodes
            if not current_nodes:
                break
        
        # Sort by relevance
        sorted_context = sorted(context_nodes.items(), key=lambda x: x[1], reverse=True)
        return sorted_context

    def _generate_embedding(self, text: str) -> List[float]:
        """Generate a simple embedding for text (in production, use a proper embedding model)."""
        # Simple hash-based embedding for PoC
        import hashlib
        hash_obj = hashlib.md5(text.encode())
        hash_hex = hash_obj.hexdigest()
        
        # Convert to float vector
        embedding = []
        for i in range(0, len(hash_hex), 2):
            embedding.append(int(hash_hex[i:i+2], 16) / 255.0)
        
        # Pad or truncate to fixed size
        target_size = 128
        if len(embedding) < target_size:
            embedding.extend([0.0] * (target_size - len(embedding)))
        else:
            embedding = embedding[:target_size]
        
        return embedding

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors."""
        if len(vec1) != len(vec2):
            return 0.0
        
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)

    def _find_edge(self, source_id: str, target_id: str, relationship_type: str) -> Optional[IKGEdge]:
        """Find an edge between two nodes with a specific relationship type."""
        for edge in self.edges:
            if (edge.source_node_id == source_id and edge.target_node_id == target_id and 
                edge.relationship_type == relationship_type):
                return edge
        return None

    def _find_edge_by_id(self, edge_id: str) -> Optional[IKGEdge]:
        """Find an edge by its ID."""
        for edge in self.edges:
            if edge.edge_id == edge_id:
                return edge
        return None

    async def _get_connected_concepts(self, node_id: str) -> List[str]:
        """Get concepts connected to a node."""
        connected = []
        edge_ids = self.edge_index.get(node_id, [])
        
        for edge_id in edge_ids:
            edge = self._find_edge_by_id(edge_id)
            if edge:
                other_node_id = edge.target_node_id if edge.source_node_id == node_id else edge.source_node_id
                other_node = self.nodes.get(other_node_id)
                if other_node:
                    connected.append(f"{other_node.node_type}: {other_node.properties.get('name', other_node_id)}")
        
        return connected[:5]  # Limit to top 5 connections
