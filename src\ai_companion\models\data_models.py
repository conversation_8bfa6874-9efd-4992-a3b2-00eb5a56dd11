"""
Core data models for the Aura AI Companion system.
Defines the fundamental data structures used throughout the application.
"""
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional, Any
from enum import Enum

class EmotionType(Enum):
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    NEUTRAL = "neutral"

class RiskLevel(Enum):
    NONE = "none"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class EmotionProfile:
    """
    Represents the emotional analysis of a single message.
    """
    primary_emotion: EmotionType
    emotion_intensity: float  # 0.0 to 1.0
    sentiment_polarity: float  # -1.0 (negative) to 1.0 (positive)
    arousal_level: float  # 0.0 (calm) to 1.0 (excited)
    is_crisis_signal: bool
    confidence_score: float  # 0.0 to 1.0
    detected_keywords: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'primary_emotion': self.primary_emotion.value,
            'emotion_intensity': self.emotion_intensity,
            'sentiment_polarity': self.sentiment_polarity,
            'arousal_level': self.arousal_level,
            'is_crisis_signal': self.is_crisis_signal,
            'confidence_score': self.confidence_score,
            'detected_keywords': self.detected_keywords
        }

@dataclass
class ConversationMessage:
    """
    Represents a single message in a conversation.
    """
    id: str
    timestamp: datetime
    sender: str  # 'user' or 'assistant'
    message: str
    emotion_profile: Optional[EmotionProfile] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'timestamp': self.timestamp.isoformat(),
            'sender': self.sender,
            'message': self.message,
            'emotion_profile': self.emotion_profile.to_dict() if self.emotion_profile else None
        }

@dataclass
class InferredInsight:
    """
    Represents an insight inferred from conversation analysis.
    """
    insight_type: str  # e.g., 'PotentialSleepTime', 'PositiveCopingMechanism', 'RelationshipSentiment'
    confidence_score: float  # 0.0 to 1.0
    timestamp: datetime
    data: Dict[str, Any]  # Flexible data structure for insight-specific information
    source_message_ids: List[str]  # References to messages that led to this insight
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'insight_type': self.insight_type,
            'confidence_score': self.confidence_score,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'source_message_ids': self.source_message_ids
        }

@dataclass
class IKGNode:
    """
    Represents a node in the Implicit Knowledge Graph.
    """
    node_id: str
    node_type: str  # e.g., 'Person', 'Activity', 'Emotion', 'Event'
    properties: Dict[str, Any]
    embedding_vector: List[float]
    creation_timestamp: datetime
    last_updated: datetime
    strength: float  # How well-established this node is (0.0 to 1.0)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'node_id': self.node_id,
            'node_type': self.node_type,
            'properties': self.properties,
            'embedding_vector': self.embedding_vector,
            'creation_timestamp': self.creation_timestamp.isoformat(),
            'last_updated': self.last_updated.isoformat(),
            'strength': self.strength
        }

@dataclass
class IKGEdge:
    """
    Represents an edge (relationship) in the Implicit Knowledge Graph.
    """
    edge_id: str
    source_node_id: str
    target_node_id: str
    relationship_type: str  # e.g., 'triggers', 'helps_with', 'related_to'
    weight: float  # Strength of the relationship (0.0 to 1.0)
    properties: Dict[str, Any]
    creation_timestamp: datetime
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'edge_id': self.edge_id,
            'source_node_id': self.source_node_id,
            'target_node_id': self.target_node_id,
            'relationship_type': self.relationship_type,
            'weight': self.weight,
            'properties': self.properties,
            'creation_timestamp': self.creation_timestamp.isoformat(),
            'last_updated': self.last_updated.isoformat()
        }

@dataclass
class ECoTStep:
    """
    Represents a single step in the Emotional Chain-of-Thought process.
    """
    step_type: str  # e.g., 'emotion_analysis', 'context_retrieval', 'response_planning'
    content: str
    confidence: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'step_type': self.step_type,
            'content': self.content,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat()
        }

@dataclass
class ECoTProcess:
    """
    Represents the complete Emotional Chain-of-Thought process for a response.
    """
    process_id: str
    user_message_id: str
    steps: List[ECoTStep]
    final_response: str
    total_processing_time: float  # in seconds
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'process_id': self.process_id,
            'user_message_id': self.user_message_id,
            'steps': [step.to_dict() for step in self.steps],
            'final_response': self.final_response,
            'total_processing_time': self.total_processing_time
        }

@dataclass
class UserProfile:
    """
    Represents a user's profile and preferences.
    """
    user_id: str
    preferences: Dict[str, Any]
    privacy_settings: Dict[str, bool]
    crisis_contacts: List[Dict[str, str]]
    therapist_info: Optional[Dict[str, str]]
    created_at: datetime
    last_active: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'user_id': self.user_id,
            'preferences': self.preferences,
            'privacy_settings': self.privacy_settings,
            'crisis_contacts': self.crisis_contacts,
            'therapist_info': self.therapist_info,
            'created_at': self.created_at.isoformat(),
            'last_active': self.last_active.isoformat()
        }
