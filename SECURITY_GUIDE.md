# 🔒 Aura AI Companion - Security Guide

## ⚠️ CRITICAL: API Key Security

**NEVER share API keys in plain text, chat messages, or public repositories.**

## 🚨 Immediate Action Required

If you've accidentally exposed an API key:

1. **Revoke the key immediately** in Google Cloud Console
2. **Generate a new API key**
3. **Update your environment variables**
4. **Check for any unauthorized usage**

## 🛡️ Secure Setup Process

### Step 1: Secure Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd aura

# Run the secure setup script
python setup_secure_env.py
```

This script will:
- ✅ Prompt for your API key securely (hidden input)
- ✅ Generate secure random secrets
- ✅ Create .env file with proper permissions
- ✅ Add .env to .gitignore automatically

### Step 2: Get Your Google API Key Safely

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. **Copy it immediately** (you won't see it again)
4. **Keep it secret** - treat it like a password

### Step 3: Configure Environment

```bash
# Your .env file should look like this:
GOOGLE_API_KEY=your_actual_api_key_here
SECRET_KEY=auto_generated_secure_key
ENVIRONMENT=development
DEBUG=true
```

## 🔐 Security Best Practices

### API Key Management

```python
# ✅ CORRECT: Use environment variables
import os
from dotenv import load_dotenv

load_dotenv()
api_key = os.getenv("GOOGLE_API_KEY")

if not api_key:
    raise ValueError("GOOGLE_API_KEY environment variable is required")
```

```python
# ❌ WRONG: Never hardcode API keys
api_key = "AIzaSyCXTpUsu7Lw_UC64jttrOpy1ejnVHcOrHI"  # DON'T DO THIS!
```

### File Permissions

```bash
# Set secure permissions on .env file
chmod 600 .env

# Verify permissions
ls -la .env
# Should show: -rw------- (owner read/write only)
```

### Git Security

```bash
# Ensure .env is in .gitignore
echo ".env" >> .gitignore
echo "*.env" >> .gitignore

# Check what would be committed
git status
# .env should NOT appear in the list
```

## 🏭 Production Security

### Environment Variables in Production

**Never use .env files in production.** Instead:

#### Heroku
```bash
heroku config:set GOOGLE_API_KEY=your_key_here
```

#### AWS
```bash
aws ssm put-parameter --name "/aura/google-api-key" --value "your_key_here" --type "SecureString"
```

#### Docker
```bash
docker run -e GOOGLE_API_KEY=your_key_here aura-app
```

#### Kubernetes
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: aura-secrets
data:
  google-api-key: <base64-encoded-key>
```

### API Key Rotation

1. **Generate new key** in Google Cloud Console
2. **Update environment variables** in all environments
3. **Test the new key** works
4. **Revoke the old key**
5. **Monitor for any failures**

## 🔍 Security Monitoring

### Usage Monitoring

```python
# Monitor API usage in Google Cloud Console
# Set up billing alerts
# Review usage patterns regularly
```

### Rate Limiting

```python
# Implement rate limiting to prevent abuse
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@limiter.limit("10/minute")
async def chat_endpoint():
    # Your chat logic here
    pass
```

### Error Handling

```python
# Never expose API keys in error messages
try:
    response = model.generate_content(prompt)
except Exception as e:
    # ❌ WRONG: Could expose sensitive info
    # return f"API Error: {str(e)}"
    
    # ✅ CORRECT: Generic error message
    logger.error(f"LLM API error: {e}")
    return "I'm having trouble connecting right now, but I'm still here for you."
```

## 🚨 Crisis Data Security

### Sensitive Data Handling

```python
# Crisis conversations require extra security
if risk_level == "critical":
    # Log securely with anonymized data
    crisis_logger.log({
        "user_id": hash_user_id(user_id),
        "timestamp": datetime.now(),
        "risk_level": risk_level,
        "intervention_provided": True
    })
```

### Data Encryption

```python
# Encrypt sensitive data at rest
from cryptography.fernet import Fernet

def encrypt_sensitive_data(data: str) -> str:
    key = os.getenv("ENCRYPTION_KEY")
    f = Fernet(key)
    return f.encrypt(data.encode()).decode()
```

## 🔒 Privacy Protection

### Differential Privacy

```python
# Add noise to protect user privacy
def apply_differential_privacy(value: float, epsilon: float = 1.0) -> float:
    noise = np.random.laplace(0, 1.0 / epsilon)
    return value + noise
```

### Data Anonymization

```python
# Hash user identifiers
import hashlib

def anonymize_user_id(user_id: str) -> str:
    return hashlib.sha256(user_id.encode()).hexdigest()[:16]
```

## 🛠️ Security Testing

### Automated Security Checks

```bash
# Install security tools
pip install bandit safety

# Run security scans
bandit -r src/
safety check

# Check for secrets in code
git-secrets --scan
```

### Manual Security Review

- [ ] No API keys in code
- [ ] .env files not committed
- [ ] Proper error handling
- [ ] Input validation
- [ ] Rate limiting implemented
- [ ] Logging doesn't expose secrets

## 📋 Security Checklist

### Development
- [ ] API keys in environment variables only
- [ ] .env file has secure permissions (600)
- [ ] .env files in .gitignore
- [ ] No secrets in code or comments
- [ ] Error messages don't expose sensitive data

### Production
- [ ] Environment variables set securely
- [ ] No .env files on production servers
- [ ] API usage monitoring enabled
- [ ] Rate limiting implemented
- [ ] Logging configured securely
- [ ] Regular security updates

### Crisis Handling
- [ ] Crisis data encrypted
- [ ] User IDs anonymized in logs
- [ ] Secure alert mechanisms
- [ ] Compliance with regulations
- [ ] Regular security audits

## 🆘 Emergency Response

### If API Key is Compromised

1. **Immediately revoke** the key in Google Cloud Console
2. **Generate new key** and update all environments
3. **Review usage logs** for unauthorized activity
4. **Check billing** for unexpected charges
5. **Update monitoring** to detect future issues

### If User Data is Compromised

1. **Assess scope** of the breach
2. **Secure the vulnerability** immediately
3. **Notify affected users** as required by law
4. **Document the incident** for compliance
5. **Review and improve** security measures

## 📞 Support

For security concerns:
- **Email**: <EMAIL>
- **Emergency**: Use secure channels only
- **Documentation**: This guide and code comments

Remember: **Security is everyone's responsibility.** When in doubt, choose the more secure option.

---

**⚠️ Final Reminder**: Never share API keys in plain text. Always use secure environment variable management.
