"""
Report Generator for the Aura AI Companion.
Generates secure, synthesized, privacy-preserving Actionable Insights Reports.
"""
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json

from ..privacy import apply_differential_privacy, anonymize_user_data, privacy_audit_logger
from ..memory.knowledge_graph_service import KnowledgeGraphService


class ReportGenerator:
    """
    Generates the secure, synthesized, privacy-preserving Actionable Insights Report.
    Creates human-readable reports for therapists while protecting user privacy.
    """
    
    def __init__(self):
        self.report_templates = self._load_report_templates()
        print("ReportGenerator Initialized.")

    def _load_report_templates(self) -> Dict[str, str]:
        """Load report templates for different types of insights."""
        return {
            'sleep_analysis': """
Sleep Pattern Analysis:
- Average sleep time: {avg_sleep_hour:.1f}:00
- Pattern consistency: {pattern_consistency:.0%}
- Data points analyzed: {data_points}
- Assessment: {sleep_assessment}
""",
            'emotional_trends': """
Emotional Trend Analysis:
- Average sentiment: {avg_sentiment:.2f} (range: -1.0 to 1.0)
- Sentiment trend: {sentiment_trend}
- Emotional volatility: {emotional_volatility:.2f}
- Assessment: {emotion_assessment}
""",
            'coping_mechanisms': """
Coping Mechanism Analysis:
- Identified positive coping strategies: {positive_coping}
- Effectiveness indicators: {effectiveness}
- Frequency of use: {frequency}
- Assessment: {coping_assessment}
""",
            'relationship_patterns': """
Relationship Pattern Analysis:
- Key relationships identified: {relationship_count}
- Overall relationship sentiment: {overall_sentiment}
- Support network indicators: {support_indicators}
- Assessment: {relationship_assessment}
"""
        }

    async def generate_therapist_report(self, user_id: str, ikg_service: KnowledgeGraphService, 
                                      time_period_days: int = 30) -> Dict[str, Any]:
        """
        Generates a comprehensive, privacy-protected report for a therapist.
        
        Args:
            user_id: User identifier
            ikg_service: Knowledge graph service instance
            time_period_days: Number of days to include in the report
            
        Returns:
            Dictionary containing the report and metadata
        """
        # Log the report generation for privacy audit
        privacy_audit_logger.log_data_access(
            user_id=user_id,
            data_type="knowledge_graph_aggregates",
            purpose="therapist_report_generation",
            accessor="report_generator"
        )
        
        # Extract raw aggregates from the knowledge graph
        raw_aggregates = await self._extract_user_aggregates(user_id, ikg_service, time_period_days)
        
        # Apply privacy-preserving techniques
        private_aggregates = apply_differential_privacy(raw_aggregates, epsilon=1.0)
        anonymized_aggregates = anonymize_user_data(private_aggregates, user_id)
        
        # Log the privacy processing
        privacy_audit_logger.log_data_processing(
            user_id=user_id,
            processing_type="report_generation",
            privacy_technique="differential_privacy_and_anonymization"
        )
        
        # Generate the human-readable report
        report_content = await self._synthesize_report(anonymized_aggregates)
        
        # Create report metadata
        report_metadata = {
            'report_id': f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            'generated_at': datetime.now().isoformat(),
            'time_period_days': time_period_days,
            'privacy_techniques_applied': ['differential_privacy', 'anonymization'],
            'data_sources': ['conversation_analysis', 'behavioral_patterns', 'emotional_trends'],
            'anonymized_user_id': anonymized_aggregates.get('user_id', 'unknown')
        }
        
        return {
            'report_content': report_content,
            'metadata': report_metadata,
            'privacy_summary': self._generate_privacy_summary(),
            'clinical_disclaimers': self._generate_clinical_disclaimers()
        }

    async def _extract_user_aggregates(self, user_id: str, ikg_service: KnowledgeGraphService, 
                                     time_period_days: int) -> Dict[str, Any]:
        """Extract aggregated data from the user's knowledge graph."""
        
        # In a real implementation, this would query the knowledge graph
        # for specific patterns and aggregate them
        
        # Simulate extracting sleep patterns
        sleep_nodes = [node for node in ikg_service.nodes.values() 
                      if node.node_type == 'SleepPattern']
        
        sleep_data = {}
        if sleep_nodes:
            sleep_node = sleep_nodes[0]  # Assume one sleep pattern node
            sleep_data = {
                'avg_sleep_time': sleep_node.properties.get('average_sleep_hour', 23.0),
                'pattern_consistency': sleep_node.properties.get('pattern_consistency', 0.7),
                'data_points': sleep_node.properties.get('data_points', 10)
            }
        
        # Extract emotional trends
        mood_nodes = [node for node in ikg_service.nodes.values() 
                     if node.node_type == 'MoodPattern']
        
        mood_data = {}
        if mood_nodes:
            mood_node = mood_nodes[0]
            mood_data = {
                'weekday_moods': mood_node.properties.get('weekday_moods', {}),
                'mood_variance': mood_node.properties.get('mood_variance', 0.3),
                'best_day': mood_node.properties.get('best_day', ['Monday', 0.5]),
                'worst_day': mood_node.properties.get('worst_day', ['Sunday', -0.2])
            }
        
        # Extract coping mechanisms
        coping_edges = [edge for edge in ikg_service.edges 
                       if edge.relationship_type == 'helps_with']
        
        coping_data = {
            'positive_coping_count': len(coping_edges),
            'avg_effectiveness': sum(edge.weight for edge in coping_edges) / len(coping_edges) if coping_edges else 0.0,
            'most_effective_mechanism': 'exercise' if coping_edges else 'none_identified'
        }
        
        # Extract relationship patterns
        relationship_nodes = [node for node in ikg_service.nodes.values() 
                            if node.node_type == 'Person']
        
        relationship_data = {
            'relationship_count': len(relationship_nodes),
            'avg_relationship_sentiment': sum(
                node.properties.get('avg_sentiment', 0.0) for node in relationship_nodes
            ) / len(relationship_nodes) if relationship_nodes else 0.0,
            'support_network_size': len([node for node in relationship_nodes 
                                       if node.properties.get('avg_sentiment', 0.0) > 0.3])
        }
        
        return {
            'user_id': user_id,
            'analysis_period': time_period_days,
            'sleep_patterns': sleep_data,
            'emotional_trends': mood_data,
            'coping_mechanisms': coping_data,
            'relationship_patterns': relationship_data,
            'generated_timestamp': datetime.now().isoformat()
        }

    async def _synthesize_report(self, aggregates: Dict[str, Any]) -> str:
        """Synthesize aggregated data into a human-readable clinical report."""
        
        report_sections = []
        
        # Executive Summary
        report_sections.append("EXECUTIVE SUMMARY")
        report_sections.append("=" * 50)
        report_sections.append(f"Analysis Period: {aggregates.get('analysis_period', 'Unknown')} days")
        report_sections.append(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        report_sections.append(f"Anonymized User ID: {aggregates.get('user_id', 'Unknown')}")
        report_sections.append("")
        
        # Sleep Analysis Section
        sleep_data = aggregates.get('sleep_patterns', {})
        if sleep_data:
            report_sections.append("SLEEP PATTERN ANALYSIS")
            report_sections.append("-" * 30)
            
            avg_sleep = sleep_data.get('avg_sleep_time', 23.0)
            consistency = sleep_data.get('pattern_consistency', 0.7)
            
            sleep_assessment = self._assess_sleep_pattern(avg_sleep, consistency)
            
            sleep_section = self.report_templates['sleep_analysis'].format(
                avg_sleep_hour=avg_sleep,
                pattern_consistency=consistency,
                data_points=sleep_data.get('data_points', 0),
                sleep_assessment=sleep_assessment
            )
            report_sections.append(sleep_section)
        
        # Emotional Trends Section
        mood_data = aggregates.get('emotional_trends', {})
        if mood_data:
            report_sections.append("EMOTIONAL TREND ANALYSIS")
            report_sections.append("-" * 30)
            
            # Calculate overall sentiment from weekday moods
            weekday_moods = mood_data.get('weekday_moods', {})
            avg_sentiment = sum(weekday_moods.values()) / len(weekday_moods) if weekday_moods else 0.0
            
            emotion_assessment = self._assess_emotional_trends(avg_sentiment, mood_data.get('mood_variance', 0.3))
            
            emotion_section = self.report_templates['emotional_trends'].format(
                avg_sentiment=avg_sentiment,
                sentiment_trend="Stable" if mood_data.get('mood_variance', 0.3) < 0.4 else "Variable",
                emotional_volatility=mood_data.get('mood_variance', 0.3),
                emotion_assessment=emotion_assessment
            )
            report_sections.append(emotion_section)
        
        # Coping Mechanisms Section
        coping_data = aggregates.get('coping_mechanisms', {})
        if coping_data:
            report_sections.append("COPING MECHANISM ANALYSIS")
            report_sections.append("-" * 30)
            
            coping_assessment = self._assess_coping_mechanisms(coping_data)
            
            coping_section = self.report_templates['coping_mechanisms'].format(
                positive_coping=coping_data.get('most_effective_mechanism', 'None identified'),
                effectiveness=f"{coping_data.get('avg_effectiveness', 0.0):.1%}",
                frequency=f"{coping_data.get('positive_coping_count', 0)} instances",
                coping_assessment=coping_assessment
            )
            report_sections.append(coping_section)
        
        # Relationship Patterns Section
        relationship_data = aggregates.get('relationship_patterns', {})
        if relationship_data:
            report_sections.append("RELATIONSHIP PATTERN ANALYSIS")
            report_sections.append("-" * 30)
            
            relationship_assessment = self._assess_relationships(relationship_data)
            
            relationship_section = self.report_templates['relationship_patterns'].format(
                relationship_count=relationship_data.get('relationship_count', 0),
                overall_sentiment=f"{relationship_data.get('avg_relationship_sentiment', 0.0):.2f}",
                support_indicators=f"{relationship_data.get('support_network_size', 0)} positive relationships",
                relationship_assessment=relationship_assessment
            )
            report_sections.append(relationship_section)
        
        # Clinical Recommendations
        report_sections.append("CLINICAL OBSERVATIONS")
        report_sections.append("-" * 30)
        report_sections.append(self._generate_clinical_observations(aggregates))
        
        return "\n".join(report_sections)

    def _assess_sleep_pattern(self, avg_sleep_hour: float, consistency: float) -> str:
        """Generate assessment of sleep patterns."""
        if avg_sleep_hour > 24 or avg_sleep_hour < 20:
            sleep_timing = "irregular sleep timing"
        elif avg_sleep_hour > 23:
            sleep_timing = "late sleep phase"
        else:
            sleep_timing = "normal sleep timing"
        
        if consistency > 0.8:
            consistency_desc = "highly consistent"
        elif consistency > 0.6:
            consistency_desc = "moderately consistent"
        else:
            consistency_desc = "inconsistent"
        
        return f"Pattern shows {sleep_timing} with {consistency_desc} schedule."

    def _assess_emotional_trends(self, avg_sentiment: float, volatility: float) -> str:
        """Generate assessment of emotional trends."""
        if avg_sentiment > 0.3:
            sentiment_desc = "generally positive emotional state"
        elif avg_sentiment < -0.3:
            sentiment_desc = "concerning negative emotional trends"
        else:
            sentiment_desc = "neutral emotional baseline"
        
        if volatility > 0.5:
            volatility_desc = "with high emotional variability"
        elif volatility > 0.3:
            volatility_desc = "with moderate emotional fluctuations"
        else:
            volatility_desc = "with stable emotional regulation"
        
        return f"Analysis indicates {sentiment_desc} {volatility_desc}."

    def _assess_coping_mechanisms(self, coping_data: Dict[str, Any]) -> str:
        """Generate assessment of coping mechanisms."""
        count = coping_data.get('positive_coping_count', 0)
        effectiveness = coping_data.get('avg_effectiveness', 0.0)
        
        if count == 0:
            return "No clear positive coping mechanisms identified in the analysis period."
        elif effectiveness > 0.7:
            return f"Strong positive coping strategies identified with high effectiveness ({effectiveness:.0%})."
        elif effectiveness > 0.4:
            return f"Moderate coping strategies present with variable effectiveness ({effectiveness:.0%})."
        else:
            return f"Some coping attempts identified but with limited apparent effectiveness ({effectiveness:.0%})."

    def _assess_relationships(self, relationship_data: Dict[str, Any]) -> str:
        """Generate assessment of relationship patterns."""
        count = relationship_data.get('relationship_count', 0)
        sentiment = relationship_data.get('avg_relationship_sentiment', 0.0)
        support_size = relationship_data.get('support_network_size', 0)
        
        if count == 0:
            return "Limited relationship data available for analysis."
        elif sentiment > 0.3 and support_size > 2:
            return "Strong support network with predominantly positive relationship patterns."
        elif sentiment > 0.1:
            return "Generally positive relationship patterns with adequate social connections."
        else:
            return "Relationship patterns suggest potential social support concerns."

    def _generate_clinical_observations(self, aggregates: Dict[str, Any]) -> str:
        """Generate overall clinical observations."""
        observations = []
        
        # Sleep observations
        sleep_data = aggregates.get('sleep_patterns', {})
        if sleep_data and sleep_data.get('pattern_consistency', 0) < 0.5:
            observations.append("- Sleep pattern irregularity may warrant attention")
        
        # Emotional observations
        mood_data = aggregates.get('emotional_trends', {})
        if mood_data and mood_data.get('mood_variance', 0) > 0.6:
            observations.append("- High emotional volatility observed")
        
        # Coping observations
        coping_data = aggregates.get('coping_mechanisms', {})
        if coping_data and coping_data.get('positive_coping_count', 0) == 0:
            observations.append("- Limited positive coping mechanisms identified")
        
        # Relationship observations
        relationship_data = aggregates.get('relationship_patterns', {})
        if relationship_data and relationship_data.get('support_network_size', 0) < 2:
            observations.append("- Social support network may benefit from strengthening")
        
        if not observations:
            observations.append("- No significant concerns identified in current analysis period")
        
        observations.append("\nNote: This analysis is based on computational patterns and should be interpreted alongside clinical assessment.")
        
        return "\n".join(observations)

    def _generate_privacy_summary(self) -> Dict[str, Any]:
        """Generate a summary of privacy protections applied."""
        return {
            'privacy_techniques': [
                'Differential Privacy (ε=1.0)',
                'Data Anonymization',
                'Aggregation-only Analysis'
            ],
            'data_protection_measures': [
                'No raw conversation content included',
                'Statistical noise added to numerical values',
                'Personal identifiers removed or hashed',
                'Time-bounded analysis window'
            ],
            'compliance_notes': [
                'Report generated in compliance with privacy regulations',
                'Data minimization principles applied',
                'Purpose limitation respected'
            ]
        }

    def _generate_clinical_disclaimers(self) -> List[str]:
        """Generate clinical and legal disclaimers for the report."""
        return [
            "This report is generated through automated analysis of behavioral patterns and should not be used as a sole basis for clinical diagnosis.",
            "All numerical values have been processed with privacy-preserving techniques and may not reflect exact measurements.",
            "This analysis supplements but does not replace direct clinical assessment and therapeutic relationship.",
            "Report should be interpreted by qualified mental health professionals familiar with the individual's clinical context.",
            "Privacy-preserving techniques may introduce statistical noise that affects precision of specific measurements."
        ]

    async def generate_user_insights_summary(self, user_id: str, ikg_service: KnowledgeGraphService) -> Dict[str, Any]:
        """
        Generate a user-facing insights summary (less detailed than therapist report).
        
        Args:
            user_id: User identifier
            ikg_service: Knowledge graph service instance
            
        Returns:
            User-friendly insights summary
        """
        # Extract basic aggregates
        raw_aggregates = await self._extract_user_aggregates(user_id, ikg_service, 7)  # Last 7 days
        
        # Apply lighter privacy protection for user-facing report
        private_aggregates = apply_differential_privacy(raw_aggregates, epsilon=2.0)  # Less noise
        
        # Generate user-friendly summary
        summary = {
            'insights_available': True,
            'time_period': '7 days',
            'patterns_detected': [],
            'positive_trends': [],
            'areas_for_attention': []
        }
        
        # Add pattern summaries
        sleep_data = private_aggregates.get('sleep_patterns', {})
        if sleep_data:
            consistency = sleep_data.get('pattern_consistency', 0.7)
            if consistency > 0.7:
                summary['positive_trends'].append("Consistent sleep schedule")
            else:
                summary['areas_for_attention'].append("Sleep schedule could be more regular")
        
        coping_data = private_aggregates.get('coping_mechanisms', {})
        if coping_data and coping_data.get('positive_coping_count', 0) > 0:
            summary['positive_trends'].append("Positive coping strategies identified")
        
        return summary
