#!/usr/bin/env python3
"""
Quick setup and launch script for Aura AI Companion Gradio Interface.
This script handles dependency installation and launches the web interface.
"""
import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """Install required packages for the Gradio interface."""
    print("🔧 Installing required packages...")
    
    # Essential packages for the Gradio interface
    essential_packages = [
        "gradio>=4.0.0",
        "google-generativeai>=0.3.0", 
        "python-dotenv>=1.0.0",
        "numpy>=1.21.0",
        "asyncio",
        "typing-extensions>=4.0.0"
    ]
    
    for package in essential_packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Warning: Failed to install {package}: {e}")
            print("You may need to install this manually.")

def check_api_key():
    """Check if Google API key is configured."""
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("\n⚠️  GOOGLE_API_KEY not found!")
        print("Aura will work with fallback responses, but for full LLM capabilities:")
        print("1. Get a Google API key from: https://makersuite.google.com/app/apikey")
        print("2. Create a .env file in this directory")
        print("3. Add: GOOGLE_API_KEY=your_api_key_here")
        print("\nContinuing with fallback mode...\n")
        return False
    else:
        print("✅ Google API key found - Full LLM integration enabled")
        return True

def create_env_template():
    """Create .env template if it doesn't exist."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from template...")
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ .env file created. Please edit it with your API keys.")

def main():
    """Main setup and launch function."""
    print("🌟 Aura AI Companion - Gradio Interface Setup")
    print("=" * 50)
    
    # Install essential packages
    install_requirements()
    
    # Create .env template
    create_env_template()
    
    # Check API key
    has_api_key = check_api_key()
    
    print("\n🚀 Launching Aura AI Companion Web Interface...")
    print("The interface will open in your browser at: http://localhost:7860")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    # Launch the Gradio app
    try:
        import gradio_app
        # The gradio_app.py will handle the rest
    except ImportError as e:
        print(f"❌ Error importing gradio_app: {e}")
        print("Make sure gradio_app.py is in the same directory.")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 Shutting down Aura AI Companion. Goodbye!")
    except Exception as e:
        print(f"❌ Error running the application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
