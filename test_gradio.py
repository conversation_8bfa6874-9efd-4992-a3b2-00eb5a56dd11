#!/usr/bin/env python3
"""
Test script to verify Gradio interface functionality.
"""
import sys
import os
import asyncio

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all required imports work."""
    print("🧪 Testing imports...")
    
    try:
        import gradio as gr
        print("✅ Gradio import successful")
    except ImportError as e:
        print(f"❌ Gradio import failed: {e}")
        return False
    
    try:
        from ai_companion import create_aura_companion
        print("✅ Aura AI Companion import successful")
    except ImportError as e:
        print(f"❌ Aura import failed: {e}")
        return False
    
    return True

async def test_aura_functionality():
    """Test basic Aura functionality."""
    print("\n🧪 Testing Aura functionality...")
    
    try:
        from ai_companion import create_aura_companion
        
        # Initialize Aura
        aura = create_aura_companion()
        print("✅ Aura initialization successful")
        
        # Create user
        await aura.create_user("test_user")
        print("✅ User creation successful")
        
        # Test chat
        response = await aura.chat("test_user", "Hello, how are you?")
        print("✅ Chat functionality successful")
        print(f"   Sample response: {response['response'][:50]}...")
        
        # Test emotion analysis
        emotion = response.get('emotion_analysis', {})
        if emotion:
            print(f"✅ Emotion analysis working: {emotion.get('primary_emotion', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Aura functionality test failed: {e}")
        return False

def test_gradio_interface():
    """Test that Gradio interface can be created."""
    print("\n🧪 Testing Gradio interface creation...")
    
    try:
        import gradio as gr
        
        # Create a simple test interface
        def test_function(text):
            return f"Echo: {text}"
        
        interface = gr.Interface(
            fn=test_function,
            inputs="text",
            outputs="text",
            title="Test Interface"
        )
        
        print("✅ Gradio interface creation successful")
        return True
        
    except Exception as e:
        print(f"❌ Gradio interface test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🌟 Aura AI Companion - Gradio Interface Tests")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed. Please install required packages:")
        print("   pip install gradio google-generativeai python-dotenv")
        return False
    
    # Test Aura functionality
    aura_test = asyncio.run(test_aura_functionality())
    if not aura_test:
        print("\n❌ Aura functionality tests failed.")
        return False
    
    # Test Gradio interface
    if not test_gradio_interface():
        print("\n❌ Gradio interface tests failed.")
        return False
    
    print("\n✅ All tests passed! The Gradio interface should work correctly.")
    print("\nTo launch the interface, run:")
    print("   python gradio_app.py")
    print("   or")
    print("   python run_gradio_app.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
